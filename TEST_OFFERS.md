# Testing Guide: Offers Feature Implementation

## 🧪 Manual Testing Steps

### 1. Test Offer Submission Fix

#### Prerequisites
- Backend server running on http://localhost:5000
- Frontend running on http://localhost:5173
- User logged in as buyer
- Content available for offers

#### Test Steps
1. **Navigate to Content Details**
   ```
   Go to: http://localhost:5173/buyer/details/{contentId}
   ```

2. **Test Countdown Timer (if auction hasn't started)**
   - Verify countdown displays above "Make Offer" button
   - Check timer updates every second
   - Verify format: "Auction starts in: HH:MM:SS"

3. **Submit Offer**
   - Click "Make Offer" button
   - Fill in offer amount and message
   - Submit offer
   - **Expected**: Success message, no "seller required" error

4. **Verify Offer Creation**
   ```bash
   # Check database or API response
   GET http://localhost:5000/api/offers/buyer
   ```

### 2. Test Buyer Offers Dashboard

#### Test Steps
1. **Navigate to Buyer Offers**
   ```
   Go to: http://localhost:5173/buyer/account/offers
   ```

2. **Verify Offers Display**
   - Check offers table loads
   - Verify status badges display correctly
   - Check content thumbnails and details

3. **Test Cancel Offer**
   - Find pending offer
   - Click cancel button
   - Confirm cancellation
   - **Expected**: Offer status changes to "Cancelled"

4. **Test View Content**
   - Click eye icon on any offer
   - **Expected**: Redirects to content details page

### 3. Test Seller Offers Dashboard

#### Test Steps
1. **Navigate to Seller Offers**
   ```
   Go to: http://localhost:5173/seller/offers
   ```

2. **Verify Received Offers**
   - Check offers table displays received offers
   - Verify buyer information shows correctly
   - Check offer messages display

3. **Test Accept Offer**
   - Click accept button on pending offer
   - Add optional response message
   - Submit acceptance
   - **Expected**: Offer status changes to "Accepted"

4. **Test Reject Offer**
   - Click reject button on pending offer
   - Add required response message
   - Submit rejection
   - **Expected**: Offer status changes to "Rejected"

### 4. Test Navigation

#### Test Steps
1. **Buyer Navigation**
   - Click "My Offers" in buyer sidebar
   - Verify active state highlighting
   - Check URL changes to `/buyer/account/offers`

2. **Seller Navigation**
   - Click "Offers" in seller sidebar
   - Verify active state highlighting
   - Check URL changes to `/seller/offers`

## 🔍 API Testing

### Test Offer Creation
```bash
# POST /api/offers
curl -X POST http://localhost:5000/api/offers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {buyer_token}" \
  -d '{
    "contentId": "6850f6a67b041819b821ce17",
    "amount": 25.00,
    "message": "Test offer message"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "_id": "...",
    "content": "6850f6a67b041819b821ce17",
    "buyer": "...",
    "seller": "...",
    "amount": 25,
    "status": "Pending",
    "message": "Test offer message"
  }
}
```

### Test Get Buyer Offers
```bash
# GET /api/offers/buyer
curl -X GET http://localhost:5000/api/offers/buyer \
  -H "Authorization: Bearer {buyer_token}"
```

### Test Get Seller Offers
```bash
# GET /api/offers/seller
curl -X GET http://localhost:5000/api/offers/seller \
  -H "Authorization: Bearer {seller_token}"
```

### Test Update Offer Status
```bash
# PUT /api/offers/{offerId}/status
curl -X PUT http://localhost:5000/api/offers/{offerId}/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {seller_token}" \
  -d '{
    "status": "accepted",
    "sellerResponse": "Thank you for your offer!"
  }'
```

### Test Cancel Offer
```bash
# PUT /api/offers/{offerId}/cancel
curl -X PUT http://localhost:5000/api/offers/{offerId}/cancel \
  -H "Authorization: Bearer {buyer_token}"
```

## 🐛 Common Issues & Solutions

### Issue: "seller required" Error
**Solution**: Check that content exists and has a seller field

### Issue: Countdown Timer Not Showing
**Solution**: Verify auction start date is in the future

### Issue: Offers Tab Not Visible
**Solution**: Check user role and authentication

### Issue: Modal Not Opening
**Solution**: Check for JavaScript errors in console

## ✅ Success Criteria

### Offer Submission
- [x] No "seller required" error
- [x] Offer created with correct seller field
- [x] Success message displayed

### Countdown Timer
- [x] Displays when auction hasn't started
- [x] Updates in real-time
- [x] Responsive design

### Buyer Offers Tab
- [x] Accessible from sidebar
- [x] Displays all buyer offers
- [x] Cancel functionality works
- [x] Status badges display correctly

### Seller Offers Tab
- [x] Accessible from sidebar
- [x] Displays received offers
- [x] Accept/reject functionality works
- [x] Response modal works

### Navigation
- [x] Tab highlighting works
- [x] URL routing correct
- [x] Mobile responsive

## 📊 Performance Checks

1. **Page Load Times**
   - Offers pages load within 2 seconds
   - No unnecessary API calls

2. **Real-time Updates**
   - Countdown timer smooth updates
   - No memory leaks

3. **Mobile Performance**
   - Touch interactions responsive
   - Layouts adapt correctly

## 🔒 Security Validation

1. **Authorization**
   - Only buyers can create offers
   - Only sellers can accept/reject offers
   - Users can only see their own offers

2. **Data Validation**
   - Offer amounts validated
   - Message length limits enforced
   - Content ownership verified
