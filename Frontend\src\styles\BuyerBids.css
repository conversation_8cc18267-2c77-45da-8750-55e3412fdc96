.BuyerBids {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerDownloads pattern */
.BuyerBids .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerBids .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerBids .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerBids .table-row:last-child {
  border-bottom: none;
}

.BuyerBids .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerBids .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  
}

.BuyerBids .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerBids .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerBids .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerBids .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerBids .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerBids .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerBids .status-badge.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerBids .status-badge.won {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerBids .status-badge.lost {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerBids .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerBids .action-btn:hover {
  color: var(--btn-color);
}

.BuyerBids__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerBids__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerBids .table-header,
  .BuyerBids .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 0.5fr;
  }

  .BuyerBids .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerBids .table {
    overflow-x: auto;
  }

  .BuyerBids .table-header,
  .BuyerBids .table-row {
    min-width: 700px;
  }
}

/* Enhanced Buyer Bids Styles */
.empty-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.auction-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.auction-indicator.active {
  background-color: #d1fae5;
  color: #065f46;
}

.auction-indicator.ended {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-pay {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #10b981;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-pay:hover {
  background-color: #059669;
}

.btn-cancel {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-cancel:hover {
  background-color: #dc2626;
}

.btn-view {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #6b7280;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background-color: #4b5563;
}

/* Cancel Modal Styles */
.cancel-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.cancel-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.cancel-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.cancel-modal .modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.cancel-modal .close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.cancel-modal .close-btn:hover {
  background-color: #f3f4f6;
}

.cancel-modal .modal-content {
  padding: 0 24px 24px;
}

.cancel-modal .bid-details {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.cancel-modal .bid-details h4 {
  margin: 0 0 12px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.cancel-modal .bid-details p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.warning-message {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.warning-message p {
  margin: 0;
  font-size: 0.875rem;
  color: #92400e;
}

.cancel-modal .modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.btn-danger {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-secondary:disabled,
.btn-danger:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
