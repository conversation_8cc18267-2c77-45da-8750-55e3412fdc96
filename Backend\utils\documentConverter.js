const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const XLSX = require('xlsx');

/**
 * Custom Document Converter Service
 * Converts Office documents to HTML/JSON for preview without external services
 */

/**
 * Convert Word document to HTML
 * @param {string} filePath - Path to the Word document
 * @returns {Promise<Object>} - Converted HTML content and metadata
 */
const convertWordToHtml = async (filePath) => {
  try {
    console.log(`[DocumentConverter] Converting Word document: ${filePath}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Word document not found: ${filePath}`);
    }

    // Convert using mammoth
    const result = await mammoth.convertToHtml({ path: filePath });
    
    // Extract text content for metadata
    const textResult = await mammoth.extractRawText({ path: filePath });
    
    const wordCount = textResult.value.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = textResult.value.length;
    
    console.log(`[DocumentConverter] Word conversion successful. Words: ${wordCount}, Characters: ${charCount}`);
    
    return {
      success: true,
      html: result.value,
      text: textResult.value,
      metadata: {
        wordCount,
        charCount,
        hasImages: result.value.includes('<img'),
        warnings: result.messages || []
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Word conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      html: null,
      text: null,
      metadata: null
    };
  }
};

/**
 * Convert Excel document to structured data
 * @param {string} filePath - Path to the Excel document
 * @returns {Promise<Object>} - Converted data and metadata
 */
const convertExcelToData = async (filePath) => {
  try {
    console.log(`[DocumentConverter] Converting Excel document: ${filePath}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel document not found: ${filePath}`);
    }

    // Read the workbook
    const workbook = XLSX.readFile(filePath);
    const sheetNames = workbook.SheetNames;
    
    const sheets = {};
    let totalRows = 0;
    let totalCells = 0;
    
    // Convert each sheet
    sheetNames.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        raw: false 
      });
      
      // Filter out empty rows
      const filteredData = jsonData.filter(row => 
        row.some(cell => cell !== null && cell !== undefined && cell !== '')
      );
      
      sheets[sheetName] = {
        data: filteredData.slice(0, 100), // Limit to first 100 rows for preview
        rowCount: filteredData.length,
        columnCount: Math.max(...filteredData.map(row => row.length), 0)
      };
      
      totalRows += filteredData.length;
      totalCells += filteredData.reduce((sum, row) => sum + row.length, 0);
    });
    
    console.log(`[DocumentConverter] Excel conversion successful. Sheets: ${sheetNames.length}, Total rows: ${totalRows}`);
    
    return {
      success: true,
      sheets,
      metadata: {
        sheetCount: sheetNames.length,
        sheetNames,
        totalRows,
        totalCells,
        hasFormulas: false // Could be enhanced to detect formulas
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Excel conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      sheets: null,
      metadata: null
    };
  }
};

/**
 * Convert PowerPoint document to structured data
 * Note: PowerPoint conversion is limited without specialized libraries
 * This provides basic metadata and structure information
 * @param {string} filePath - Path to the PowerPoint document
 * @returns {Promise<Object>} - Basic document information
 */
const convertPowerPointToData = async (filePath) => {
  try {
    console.log(`[DocumentConverter] Analyzing PowerPoint document: ${filePath}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`PowerPoint document not found: ${filePath}`);
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    
    // For now, we'll provide basic file information
    // In a full implementation, you might use libraries like 'officegen' or 'node-pptx'
    // or implement a custom PPTX parser
    
    console.log(`[DocumentConverter] PowerPoint analysis complete. Size: ${fileSize} bytes`);
    
    return {
      success: true,
      metadata: {
        fileSize,
        fileName: path.basename(filePath),
        fileType: 'PowerPoint Presentation',
        lastModified: stats.mtime,
        // Note: Slide count and content extraction would require specialized libraries
        slideCount: 'Unknown',
        hasImages: 'Unknown',
        hasAnimations: 'Unknown'
      },
      preview: {
        type: 'metadata',
        message: 'PowerPoint preview shows document information. Full content preview requires download.'
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] PowerPoint analysis failed:`, error);
    return {
      success: false,
      error: error.message,
      metadata: null,
      preview: null
    };
  }
};

/**
 * Main document conversion function
 * @param {string} filePath - Path to the document
 * @param {string} fileExtension - File extension (.docx, .xlsx, etc.)
 * @returns {Promise<Object>} - Conversion result
 */
const convertDocument = async (filePath, fileExtension) => {
  const ext = fileExtension.toLowerCase();
  
  console.log(`[DocumentConverter] Starting conversion for ${ext} document: ${filePath}`);
  
  try {
    switch (ext) {
      case '.doc':
      case '.docx':
      case '.docm':
      case '.dot':
      case '.dotx':
      case '.dotm':
        return await convertWordToHtml(filePath);
        
      case '.xls':
      case '.xlsx':
      case '.xlsm':
      case '.xlt':
      case '.xltx':
      case '.xltm':
      case '.csv':
        return await convertExcelToData(filePath);
        
      case '.ppt':
      case '.pptx':
      case '.pptm':
      case '.pot':
      case '.potx':
      case '.potm':
      case '.pps':
      case '.ppsx':
      case '.ppsm':
        return await convertPowerPointToData(filePath);
        
      default:
        console.log(`[DocumentConverter] Unsupported file type: ${ext}`);
        return {
          success: false,
          error: `Unsupported document type: ${ext}`,
          supportedTypes: ['.docx', '.xlsx', '.pptx', '.doc', '.xls', '.ppt']
        };
    }
  } catch (error) {
    console.error(`[DocumentConverter] Conversion failed for ${ext}:`, error);
    return {
      success: false,
      error: `Document conversion failed: ${error.message}`,
      filePath,
      fileExtension: ext
    };
  }
};

/**
 * Check if document type is supported for conversion
 * @param {string} fileExtension - File extension
 * @returns {boolean} - Whether the type is supported
 */
const isSupportedDocumentType = (fileExtension) => {
  const supportedTypes = [
    '.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm',
    '.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv',
    '.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm'
  ];
  
  return supportedTypes.includes(fileExtension.toLowerCase());
};

/**
 * Get document type category
 * @param {string} fileExtension - File extension
 * @returns {string} - Document type (word, excel, powerpoint, unknown)
 */
const getDocumentType = (fileExtension) => {
  const ext = fileExtension.toLowerCase();
  
  if (['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'].includes(ext)) {
    return 'word';
  }
  
  if (['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'].includes(ext)) {
    return 'excel';
  }
  
  if (['.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm'].includes(ext)) {
    return 'powerpoint';
  }
  
  return 'unknown';
};

module.exports = {
  convertDocument,
  convertWordToHtml,
  convertExcelToData,
  convertPowerPointToData,
  isSupportedDocumentType,
  getDocumentType
};
