/* SellerBids Component Styles */
.seller-bids-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  
  border-radius: var(--border-radius-large);
}

.bids-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
  
}

.bids-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
}

.bids-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.action-icon-container{
  display:flex;
  align-items:center;
  justify-content:center;
 
}

.video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

.threedoticon {
font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}
.threedoticon:hover{
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
.action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}

/* Enhanced Bid Management Styles */
.bids-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.bids-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.bids-header p {
  margin: 8px 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.refresh-btn {
  background: none;
  border: 1px solid #d1d5db;
  padding: 8px;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.no-bids {
  text-align: center;
  padding: 48px 24px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-bids-icon {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-bids h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
}

.no-bids p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.content-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.content-title {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.content-type {
  font-size: 0.75rem;
  color: #6b7280;
}

.bidder-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bidder-name {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.bidder-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-active {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-won {
  background-color: #d1fae5;
  color: #065f46;
}

.status-lost {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

.auction-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.auction-status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.auction-status.ended {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-review {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-review:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-review:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-view {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #6b7280;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background-color: #4b5563;
}

/* Bid Review Modal Styles */
.bid-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.bid-review-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #f3f4f6;
}

.modal-content {
  padding: 0 24px 24px;
}

.bid-details {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.bid-details h4 {
  margin: 0 0 12px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.bid-details p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.response-section {
  margin-bottom: 24px;
}

.response-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.response-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  box-sizing: border-box;
}

.response-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.response-section small {
  display: block;
  text-align: right;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.btn-reject {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-reject:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-accept {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #10b981;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-accept:hover:not(:disabled) {
  background-color: #059669;
}

.btn-reject:disabled,
.btn-accept:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-note {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px;
}

.modal-note p {
  margin: 0;
  font-size: 0.875rem;
  color: #92400e;
}
