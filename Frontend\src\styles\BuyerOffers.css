/* Buyer Offers Styles */
.BuyerOffers {
  width: 100%;
}

.offers-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-medium);
  border-left: 4px solid var(--primary-color);
}

.offers-summary p {
  margin: 0;
  font-weight: 500;
  color: var(--secondary-color);
}

/* Content Info Styles */
.content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-thumbnail {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-small);
  overflow: hidden;
  flex-shrink: 0;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-thumbnail {
  width: 100%;
  height: 100%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-size: 20px;
}

.content-details {
  flex: 1;
  min-width: 0;
}

.content-title {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content-sport {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
  text-transform: capitalize;
}

/* Seller Info Styles */
.seller-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.seller-name {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
}

.seller-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Offer Amount Styles */
.offer-amount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Status Badge Styles */
.status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-accepted {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-cancelled {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

.status-expired {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Date Styles */
.offer-date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-small);
  border: 1px solid;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-view {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-view:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn-cancel {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-cancel:hover:not(:disabled) {
  background-color: #dc3545;
  color: white;
}

.btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* No Offers State */
.no-offers {
  text-align: center;
  padding: 60px 20px;
  color: var(--dark-gray);
}

.no-offers-icon {
  font-size: 64px;
  color: var(--light-gray);
  margin-bottom: 20px;
}

.no-offers h3 {
  font-size: var(--heading4);
  color: var(--secondary-color);
  margin-bottom: 12px;
}

.no-offers p {
  font-size: var(--smallfont);
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-info {
    gap: 8px;
  }

  .content-thumbnail {
    width: 40px;
    height: 40px;
  }

  .content-title {
    font-size: var(--extrasmallfont);
  }

  .content-sport,
  .seller-email,
  .offer-date {
    font-size: 10px;
  }

  .action-buttons {
    gap: 4px;
  }

  .btn-icon {
    width: 28px;
    height: 28px;
  }

  .no-offers {
    padding: 40px 16px;
  }

  .no-offers-icon {
    font-size: 48px;
  }
}
