import React from "react";
import { Link } from "react-router-dom";
import "../../styles/StrategyCard.css";
import { FaPlay } from "react-icons/fa";

const StrategyCard = ({
  image,
  title,
  coach,
  price,
  hasVideo,
  id,
  type = "buy",
  saleType = "Fixed",
  auctionDetails = {},
  isSold = false,
}) => {

  // Function to determine button text and type based on auction timing
  const getButtonInfo = () => {
    if (isSold) {
      return { text: "SOLD", type: "sold", disabled: true };
    }

    if (saleType === "Fixed") {
      return { text: "Buy Now", type: "buy", disabled: false };
    }

    if (saleType === "Auction") {
      const now = new Date();
      const startDate = auctionDetails.auctionStartDate ? new Date(auctionDetails.auctionStartDate) : null;
      const endDate = auctionDetails.auctionEndDate ? new Date(auctionDetails.auctionEndDate) : null;

      // Before auction starts
      if (startDate && now < startDate) {
        if (auctionDetails.allowOfferBeforeAuctionStart) {
          return { text: "Make Offer", type: "offer", disabled: false };
        } else {
          return { text: "Auction Starts Soon", type: "pending", disabled: true };
        }
      }

      // During auction
      if (startDate && endDate && now >= startDate && now <= endDate) {
        return { text: "Bid Now", type: "bid", disabled: false };
      }

      // After auction
      if (endDate && now > endDate) {
        return { text: "Auction Ended", type: "ended", disabled: true };
      }

      // Default for auction without specific dates
      return { text: "Bid Now", type: "bid", disabled: false };
    }

    // Default fallback
    return { text: "View Details", type: "view", disabled: false };
  };

  const buttonInfo = getButtonInfo();
  return (
    <div className="strategy-card-component strategy-card">
      <div className="strategy-card-image">
        <img src={image} alt={title} />
        {hasVideo && (
          <div className="video-icon">
            <FaPlay />
          </div>
        )}
      </div>
      <div className="strategy-card-content">
        <h3 className="strategy-card-title">{title}</h3>
        <p className="strategy-card-coach">By {coach}</p>
        <div className="strategy-card-footer">
          <span className="strategy-card-price">
            {saleType === "Auction" && auctionDetails.basePrice ?
              `Starting: $${auctionDetails.basePrice.toFixed(2)}` :
              `$${price?.toFixed(2) || '0.00'}`
            }
          </span>
          {buttonInfo.disabled ? (
            <button className={`action-button action-button--${buttonInfo.type}`} disabled>
              {buttonInfo.text}
            </button>
          ) : (
            <Link
              to={`/buyer/details/${id}`}
              className={`action-button action-button--${buttonInfo.type}`}
            >
              {buttonInfo.text}
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default StrategyCard;
