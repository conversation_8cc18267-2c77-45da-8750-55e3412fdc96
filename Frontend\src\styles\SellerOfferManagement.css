.seller-offer-management {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.offer-management-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.offer-management-header h2 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.offer-management-header p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.no-offers {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.no-offers h3 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 500;
}

.no-offers p {
  margin: 0;
  font-size: 0.875rem;
}

.offers-table-container {
  overflow-x: auto;
}

.offers-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.offers-table th,
.offers-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.offers-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
}

.content-title {
  font-weight: 500;
  color: #111827;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.buyer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.buyer-name {
  font-weight: 500;
  color: #111827;
}

.buyer-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.offer-amount {
  font-weight: 600;
  color: #059669;
  font-size: 1rem;
}

.offer-message {
  max-width: 200px;
}

.no-message {
  color: #9ca3af;
  font-style: italic;
}

.offer-date {
  color: #6b7280;
  font-size: 0.75rem;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-accepted {
  background-color: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

.status-expired {
  background-color: #fde68a;
  color: #92400e;
}

.offer-actions {
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-accept {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-accept:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-accept:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-completed {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Modal Styles */
.offer-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.offer-review-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #f3f4f6;
}

.modal-content {
  padding: 0 24px 24px;
}

.offer-details {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.offer-details h4 {
  margin: 0 0 12px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.offer-details p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.buyer-message {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.buyer-message p {
  background-color: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin: 8px 0 0 0;
  font-style: italic;
}

.response-section {
  margin-bottom: 24px;
}

.response-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.response-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  box-sizing: border-box;
}

.response-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.response-section small {
  display: block;
  text-align: right;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.btn-reject {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-reject:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-reject:disabled,
.btn-accept:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .seller-offer-management {
    padding: 16px;
  }
  
  .offers-table {
    font-size: 0.75rem;
  }
  
  .offers-table th,
  .offers-table td {
    padding: 8px;
  }
  
  .content-title {
    max-width: 120px;
  }
  
  .offer-message {
    max-width: 100px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions button {
    width: 100%;
  }
}
