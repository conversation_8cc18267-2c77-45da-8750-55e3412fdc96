const ErrorResponse = require("../utils/errorResponse");
const Payment = require("../models/Payment");
const Order = require("../models/Order");
const User = require("../models/User");
const Card = require("../models/Card");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const { validationResult } = require("express-validator");

// Helper function to extract and save card details from payment intent
const extractAndSaveCardDetails = async (paymentIntent, userId) => {
  try {
    // Get the payment method from Stripe
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentIntent.payment_method);

    if (!paymentMethod || !paymentMethod.card) {
      return null;
    }

    const cardDetails = {
      cardType: paymentMethod.card.brand,
      lastFourDigits: paymentMethod.card.last4
    };

    // Check if this card already exists for the user
    const existingCard = await Card.findOne({
      user: userId,
      fingerprint: paymentMethod.card.fingerprint,
      isActive: true
    });

    if (existingCard) {
      // Card already exists, return reference to it
      cardDetails.cardId = existingCard._id;
      return cardDetails;
    }

    // Check if user wants to save new cards automatically (you can add this as user preference)
    // For now, we'll save all new cards used in payments
    try {
      // Get or create Stripe customer for user
      const user = await User.findById(userId);
      let stripeCustomerId = user.paymentInfo?.stripeCustomerId;

      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            userId: user.id
          }
        });

        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await User.findByIdAndUpdate(userId, {
          'paymentInfo.stripeCustomerId': stripeCustomerId
        });
      }

      // Attach payment method to customer if not already attached
      if (!paymentMethod.customer) {
        await stripe.paymentMethods.attach(paymentMethod.id, {
          customer: stripeCustomerId,
        });
      }

      // Check if this is the user's first card to make it default
      const userCardCount = await Card.countDocuments({ user: userId, isActive: true });
      const isDefault = userCardCount === 0;

      // Create card record
      const newCard = await Card.create({
        user: userId,
        stripePaymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card.last4,
        cardType: paymentMethod.card.brand,
        expiryMonth: paymentMethod.card.exp_month,
        expiryYear: paymentMethod.card.exp_year,
        cardholderName: paymentMethod.billing_details.name || `${user.firstName} ${user.lastName}`,
        fingerprint: paymentMethod.card.fingerprint,
        isDefault,
        billingAddress: {
          line1: paymentMethod.billing_details.address?.line1,
          line2: paymentMethod.billing_details.address?.line2,
          city: paymentMethod.billing_details.address?.city,
          state: paymentMethod.billing_details.address?.state,
          postalCode: paymentMethod.billing_details.address?.postal_code,
          country: paymentMethod.billing_details.address?.country
        }
      });

      cardDetails.cardId = newCard._id;
      return cardDetails;
    } catch (cardSaveError) {
      console.error('Error saving card details:', cardSaveError);
      // Return basic card details even if saving fails
      return cardDetails;
    }
  } catch (error) {
    console.error('Error extracting card details:', error);
    return null;
  }
};

// @desc    Get all payments
// @route   GET /api/payments
// @access  Private/Admin
exports.getPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find()
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single payment
// @route   GET /api/payments/:id
// @access  Private
exports.getPayment = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order");

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is payment buyer or seller or admin
    if (
      payment.buyer._id.toString() !== req.user.id &&
      payment.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this payment`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create payment intent
// @route   POST /api/payments/create-intent
// @access  Private/Buyer
exports.createPaymentIntent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { orderId } = req.body;

    // Get order
    const order = await Order.findById(orderId).populate("content");

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (order.buyer.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to pay for this order`,
          403
        )
      );
    }

    // Check if order is already paid
    if (order.paymentStatus === "Completed") {
      return next(new ErrorResponse(`Order is already paid`, 400));
    }

    // Get or create Stripe customer for user
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id
        }
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        'paymentInfo.stripeCustomerId': stripeCustomerId
      });
    }

    // Create payment intent with required fields for Indian regulations
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(order.amount * 100), // Convert to cents
      currency: "usd",
      customer: stripeCustomerId, // Add customer to payment intent
      description: `Payment for ${order.content.title || 'Digital Content'} - Order #${order._id}`,
      metadata: {
        orderId: order._id.toString(),
        contentId: order.content._id.toString(),
        buyerId: req.user.id,
        sellerId: order.seller.toString(),
        orderType: order.orderType,
      },
      // Required for Indian regulations
      shipping: {
        name: req.user.name || 'Digital Content Buyer',
        address: {
          line1: 'Digital Content Delivery',
          city: 'Online',
          state: 'Digital',
          postal_code: '000000',
          country: 'IN',
        },
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Confirm payment
// @route   POST /api/payments/confirm
// @access  Private/Buyer
exports.confirmPayment = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { paymentIntentId, orderId } = req.body;

    // Get order
    const order = await Order.findById(orderId);

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (order.buyer.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to confirm payment for this order`,
          403
        )
      );
    }

    // Get payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return next(new ErrorResponse(`Payment has not been completed`, 400));
    }

    // Extract and save card details
    const cardDetails = await extractAndSaveCardDetails(paymentIntent, req.user.id);

    // Update order
    order.paymentStatus = "Completed";
    order.paymentIntentId = paymentIntentId;
    order.status = "Completed";

    // Add card details to order if available
    if (cardDetails) {
      order.cardDetails = {
        cardType: cardDetails.cardType,
        lastFourDigits: cardDetails.lastFourDigits
      };
    }

    await order.save();

    // Create payment record with card details
    const paymentData = {
      order: orderId,
      buyer: order.buyer,
      seller: order.seller,
      amount: order.amount,
      platformFee: order.platformFee,
      sellerEarnings: order.sellerEarnings,
      paymentMethod: "card",
      paymentIntentId,
      status: "Completed",
      payoutStatus: "Pending",
    };

    // Add card details if available
    if (cardDetails) {
      paymentData.cardDetails = cardDetails;
    }

    const payment = await Payment.create(paymentData);

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process Stripe webhook
// @route   POST /api/payments/webhook
// @access  Public
exports.webhook = async (req, res, next) => {
  try {
    let event;

    if (process.env.NODE_ENV === "development") {
      // 🔓 Bypass signature verification in development
      event = req.body;
    } else {
      // 🔐 Verify signature in production
      const sig = req.headers["stripe-signature"];

      try {
        event = stripe.webhooks.constructEvent(
          req.body,
          sig,
          process.env.STRIPE_WEBHOOK_SECRET
        );
      } catch (err) {
        return res.status(400).send(`Webhook Error: ${err.message}`);
      }
    }

    // Handle the event
    if (event.type === "payment_intent.succeeded") {
      const paymentIntent = event.data.object;
      const orderId = paymentIntent.metadata?.orderId;

      if (orderId) {
        const order = await Order.findById(orderId);

        if (order) {
          // Extract and save card details
          const cardDetails = await extractAndSaveCardDetails(paymentIntent, order.buyer);

          order.paymentStatus = "Completed";
          order.paymentIntentId = paymentIntent.id;
          order.status = "Completed";

          // Add card details to order if available
          if (cardDetails) {
            order.cardDetails = {
              cardType: cardDetails.cardType,
              lastFourDigits: cardDetails.lastFourDigits
            };
          }

          await order.save();

          const existingPayment = await Payment.findOne({
            paymentIntentId: paymentIntent.id,
          });

          if (!existingPayment) {
            // Create payment record with card details (reuse the same cardDetails)
            const paymentData = {
              order: orderId,
              buyer: order.buyer,
              seller: order.seller,
              amount: order.amount,
              platformFee: order.platformFee,
              sellerEarnings: order.sellerEarnings,
              paymentMethod: "card",
              paymentIntentId: paymentIntent.id,
              status: "Completed",
              payoutStatus: "Pending",
            };

            // Add card details if available
            if (cardDetails) {
              paymentData.cardDetails = cardDetails;
            }

            await Payment.create(paymentData);
          }
        }
      }
    }

    res.status(200).json({ received: true });
  } catch (err) {
    next(err);
  }
};

// @desc    Get buyer payments
// @route   GET /api/payments/buyer
// @access  Private/Buyer
exports.getBuyerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ buyer: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller payments
// @route   GET /api/payments/seller
// @access  Private/Seller
exports.getSellerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ seller: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process payout
// @route   POST /api/payments/:id/payout
// @access  Private/Admin
exports.processPayout = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if payment is completed
    if (payment.status !== "Completed") {
      return next(
        new ErrorResponse(`Payment must be completed to process payout`, 400)
      );
    }

    // Check if payout is already processed
    if (payment.payoutStatus === "Completed") {
      return next(new ErrorResponse(`Payout has already been processed`, 400));
    }

    // Get seller
    const seller = await User.findById(payment.seller);

    if (!seller) {
      return next(
        new ErrorResponse(`Seller not found with id of ${payment.seller}`, 404)
      );
    }

    // Check if seller has Stripe Connect ID
    if (!seller.paymentInfo || !seller.paymentInfo.stripeConnectId) {
      return next(
        new ErrorResponse(
          `Seller does not have payment information set up`,
          400
        )
      );
    }

    // Process payout through Stripe
    const payout = await stripe.transfers.create({
      amount: Math.round(payment.sellerEarnings * 100), // Convert to cents
      currency: "usd",
      destination: seller.paymentInfo.stripeConnectId,
      transfer_group: `ORDER_${payment.order}`,
    });

    // Update payment
    payment.payoutStatus = "Completed";
    payment.payoutId = payout.id;
    payment.payoutDate = Date.now();
    await payment.save();

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create payment intent for bid
// @route   POST /api/payments/create-bid-payment-intent
// @access  Private/Buyer
exports.createBidPaymentIntent = async (req, res, next) => {
  try {
    const { bidId, amount } = req.body;

    // Validate bid
    const Bid = require('../models/Bid');
    const bid = await Bid.findById(bidId)
      .populate('content', 'title seller')
      .populate('bidder', 'firstName lastName email');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    // Check if user is the bidder
    if (bid.bidder._id.toString() !== req.user.id) {
      return next(new ErrorResponse('Not authorized to pay for this bid', 403));
    }

    // Check if bid is won
    if (bid.status !== 'Won') {
      return next(new ErrorResponse('This bid has not been accepted yet', 400));
    }

    // Check if already paid
    const Order = require('../models/Order');
    const existingOrder = await Order.findOne({ bidId: bidId, status: 'Completed' });
    if (existingOrder) {
      return next(new ErrorResponse('This bid has already been paid for', 400));
    }

    // Calculate total amount (bid amount + platform fee)
    const platformFeeRate = 0.1; // 10%
    const platformFee = amount * platformFeeRate;
    const totalAmount = amount + platformFee;

    // Get or create Stripe customer for user
    const User = require('../models/User');
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id
        }
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        'paymentInfo.stripeCustomerId': stripeCustomerId
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Convert to cents
      currency: 'usd',
      customer: stripeCustomerId,
      metadata: {
        type: 'bid_payment',
        bidId: bidId,
        buyerId: req.user.id,
        sellerId: bid.content.seller.toString(),
        contentId: bid.content._id.toString(),
        bidAmount: amount.toString(),
        platformFee: platformFee.toString()
      },
      description: `Payment for winning bid on "${bid.content.title}"`,
      shipping: {
        name: req.user.name || 'Digital Content Buyer',
        address: {
          line1: 'Digital Content Delivery',
          city: 'Online',
          state: 'Digital',
          postal_code: '000000',
          country: 'IN',
        },
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Error creating bid payment intent:', error);
    next(error);
  }
};

// @desc    Complete bid purchase after payment
// @route   POST /api/payments/complete-bid-purchase
// @access  Private/Buyer
exports.completeBidPurchase = async (req, res, next) => {
  try {
    const { bidId, paymentIntentId } = req.body;

    // Verify payment intent
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return next(new ErrorResponse('Payment not completed', 400));
    }

    // Get bid details
    const Bid = require('../models/Bid');
    const bid = await Bid.findById(bidId)
      .populate('content')
      .populate('bidder')
      .populate('content.seller');

    if (!bid) {
      return next(new ErrorResponse('Bid not found', 404));
    }

    // Check if user is the bidder
    if (bid.bidder._id.toString() !== req.user.id) {
      return next(new ErrorResponse('Not authorized', 403));
    }

    // Extract and save card details
    const cardDetails = await extractAndSaveCardDetails(paymentIntent, req.user.id);

    // Create order
    const Order = require('../models/Order');
    const order = await Order.create({
      buyer: bid.bidder._id,
      seller: bid.content.seller._id,
      content: bid.content._id,
      bidId: bidId,
      orderType: 'Auction',
      amount: bid.amount,
      platformFee: bid.amount * 0.1,
      sellerEarnings: bid.amount * 0.9,
      paymentIntentId: paymentIntentId,
      status: 'Completed',
      paymentStatus: 'Completed',
      cardDetails: cardDetails ? {
        cardType: cardDetails.cardType,
        lastFourDigits: cardDetails.lastFourDigits
      } : undefined
    });

    // Create payment record
    const Payment = require('../models/Payment');
    const paymentData = {
      order: order._id,
      buyer: bid.bidder._id,
      seller: bid.content.seller._id,
      amount: bid.amount,
      platformFee: bid.amount * 0.1,
      sellerEarnings: bid.amount * 0.9,
      paymentMethod: "card",
      paymentIntentId,
      status: "Completed",
      payoutStatus: "Pending",
    };

    // Add card details if available
    if (cardDetails) {
      paymentData.cardDetails = cardDetails;
    }

    await Payment.create(paymentData);

    // Mark content as sold
    bid.content.isSold = true;
    bid.content.soldAt = new Date();
    await bid.content.save();

    // Update all other bids for this content to Lost
    await Bid.updateMany(
      { content: bid.content._id, status: 'Active', _id: { $ne: bid._id } },
      { status: 'Lost' }
    );

    res.status(200).json({
      success: true,
      message: 'Purchase completed successfully',
      data: {
        orderId: order._id,
        downloadUrl: `/api/content/${bid.content._id}/download`
      }
    });

  } catch (error) {
    console.error('Error completing bid purchase:', error);
    next(error);
  }
};
