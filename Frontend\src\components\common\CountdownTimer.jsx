import React, { useState, useEffect } from "react";
import "../../styles/CountdownTimer.css";

const CountdownTimer = ({ 
  targetDate, 
  onComplete, 
  prefix = "Auction starts in:", 
  className = "",
  showDays = true,
  size = "medium" 
}) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    total: 0
  });

  const calculateTimeLeft = () => {
    const now = new Date().getTime();
    const target = new Date(targetDate).getTime();
    const difference = target - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return {
        days,
        hours,
        minutes,
        seconds,
        total: difference
      };
    }

    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      total: 0
    };
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      if (newTimeLeft.total <= 0 && onComplete) {
        onComplete();
        clearInterval(timer);
      }
    }, 1000);

    // Set initial time
    setTimeLeft(calculateTimeLeft());

    return () => clearInterval(timer);
  }, [targetDate, onComplete]);

  // Don't render if time has passed
  if (timeLeft.total <= 0) {
    return null;
  }

  const formatTime = (time) => String(time).padStart(2, '0');

  return (
    <div className={`countdown-timer ${size} ${className}`}>
      {prefix && <div className="countdown-prefix">{prefix}</div>}
      <div className="countdown-display">
        {showDays && timeLeft.days > 0 && (
          <div className="countdown-item">
            <span className="countdown-number">{formatTime(timeLeft.days)}</span>
            <span className="countdown-label">DAYS</span>
          </div>
        )}
        <div className="countdown-item">
          <span className="countdown-number">{formatTime(timeLeft.hours)}</span>
          <span className="countdown-label">HOURS</span>
        </div>
        <div className="countdown-item">
          <span className="countdown-number">{formatTime(timeLeft.minutes)}</span>
          <span className="countdown-label">MINUTES</span>
        </div>
        <div className="countdown-item">
          <span className="countdown-number">{formatTime(timeLeft.seconds)}</span>
          <span className="countdown-label">SECONDS</span>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
