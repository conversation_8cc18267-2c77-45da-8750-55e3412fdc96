import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { getSellerBids, updateBidStatus } from "../../redux/slices/bidSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { IoEyeSharp } from "react-icons/io5";
import { SlEye } from "react-icons/sl";
import { FaGavel, FaCheck, FaTimes, FaClock, FaSync } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/SellerBids.css";
const SellerBids = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sellerBids, isLoading, isError, error } = useSelector((state) => state.bid);

  const [selectedBid, setSelectedBid] = useState(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [isResponding, setIsResponding] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    dispatch(getSellerBids());
  }, [dispatch]);

  const handleViewDetails = (bidId) => {
    navigate(`/seller/bid-details/${bidId.replace('#', '')}`);
  };

  const handleAcceptBid = async (bid) => {
    setIsResponding(true);
    try {
      await dispatch(updateBidStatus({
        bidId: bid._id,
        status: "accepted",
        sellerResponse: responseMessage
      })).unwrap();

      toast.success("Bid accepted successfully! Order has been created.");
      setShowModal(false);
      setSelectedBid(null);
      setResponseMessage("");

      // Refresh bids
      dispatch(getSellerBids());
    } catch (error) {
      toast.error(error.message || "Failed to accept bid");
    } finally {
      setIsResponding(false);
    }
  };

  const handleRejectBid = async (bid) => {
    setIsResponding(true);
    try {
      await dispatch(updateBidStatus({
        bidId: bid._id,
        status: "rejected",
        sellerResponse: responseMessage
      })).unwrap();

      toast.success("Bid rejected");
      setShowModal(false);
      setSelectedBid(null);
      setResponseMessage("");

      // Refresh bids
      dispatch(getSellerBids());
    } catch (error) {
      toast.error(error.message || "Failed to reject bid");
    } finally {
      setIsResponding(false);
    }
  };

  const openBidModal = (bid) => {
    setSelectedBid(bid);
    setShowModal(true);
    setResponseMessage("");
  };

  const closeBidModal = () => {
    setShowModal(false);
    setSelectedBid(null);
    setResponseMessage("");
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Active":
        return "status-active";
      case "Won":
        return "status-won";
      case "Lost":
        return "status-lost";
      case "Cancelled":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isAuctionActive = (content) => {
    if (!content?.auctionDetails) return false;

    const now = new Date();
    const startDate = content.auctionDetails.auctionStartDate ? new Date(content.auctionDetails.auctionStartDate) : null;
    const endDate = content.auctionDetails.auctionEndDate ? new Date(content.auctionDetails.auctionEndDate) : null;

    return (!startDate || now >= startDate) && (!endDate || now <= endDate);
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    { key: "bidId", label: "Bid Id" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (bid) => (
        <div className="video-doc">
          <img
            src={bid.content?.thumbnailUrl || 'https://via.placeholder.com/60x40'}
            alt={bid.content?.title || 'Content'}
          />
          <div className="content-details">
            <span className="content-title">{bid.content?.title || 'Unknown Content'}</span>
            <span className="content-type">{bid.content?.contentType || 'Unknown'}</span>
          </div>
        </div>
      ),
    },
    {
      key: "bidder",
      label: "Bidder",
      render: (bid) => (
        <div className="bidder-info">
          <span className="bidder-name">
            {bid.bidder?.firstName} {bid.bidder?.lastName}
          </span>
          <span className="bidder-email">{bid.bidder?.email}</span>
        </div>
      )
    },
    {
      key: "date",
      label: "Date",
      render: (bid) => formatDate(bid.createdAt)
    },
    {
      key: "bidAmount",
      label: "Bid Amount",
      render: (bid) => `$${bid.amount?.toFixed(2) || '0.00'}`
    },
    {
      key: "status",
      label: "Status",
      render: (bid) => (
        <span className={`status-badge ${getStatusBadgeClass(bid.status)}`}>
          {bid.status}
        </span>
      )
    },
    {
      key: "auctionStatus",
      label: "Auction",
      render: (bid) => (
        <span className={`auction-status ${isAuctionActive(bid.content) ? 'active' : 'ended'}`}>
          {isAuctionActive(bid.content) ? 'Active' : 'Ended'}
        </span>
      )
    },
    {
      key: "action",
      label: "Action",
      render: (bid) => (
        <div className="action-buttons">
          {bid.status === "Active" ? (
            <button
              className="btn-review"
              onClick={() => openBidModal(bid)}
              disabled={isResponding}
            >
              <FaGavel /> Review
            </button>
          ) : (
            <button
              className="btn-view"
              onClick={() => handleViewDetails(bid._id)}
            >
              <SlEye /> View
            </button>
          )}
        </div>
      ),
    },
  ];

  const formatData = (bids) => {
    return bids.map((bid, index) => ({
      ...bid,
      no: index + 1,
      bidId: `#${bid._id?.slice(-6) || 'N/A'}`,
    }));
  };

  const handleRetry = () => {
    dispatch(getSellerBids());
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="seller-bids-container">
          <div className="bids-header">
            <h2><FaGavel /> Auction Bids</h2>
            <p>Manage bids on your auction content</p>
          </div>
          <LoadingSkeleton type="table" />
        </div>
      </SellerLayout>
    );
  }

  if (isError) {
    return (
      <SellerLayout>
        <div className="seller-bids-container">
          <div className="bids-header">
            <h2><FaGavel /> Auction Bids</h2>
            <p>Manage bids on your auction content</p>
          </div>
          <ErrorDisplay
            title="Error Loading Bids"
            message={error?.message || "Failed to load bids"}
            onRetry={handleRetry}
          />
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="seller-bids-container">
        <div className="bids-header">
          <h2><FaGavel /> Auction Bids</h2>
          <p>Manage bids on your auction content</p>
          <button
            className="refresh-btn"
            onClick={handleRetry}
            title="Refresh bids"
          >
            <FaSync />
          </button>
        </div>

        {sellerBids.length === 0 ? (
          <div className="no-bids">
            <FaGavel className="no-bids-icon" />
            <h3>No bids yet</h3>
            <p>When buyers place bids on your auction content, they'll appear here.</p>
          </div>
        ) : (
          <Table
            columns={columns}
            data={formatData(sellerBids)}
            className="bids-table"
          />
        )}

        {/* Bid Review Modal */}
        {showModal && selectedBid && (
          <div className="bid-modal-overlay">
            <div className="bid-review-modal">
              <div className="modal-header">
                <h3>Review Bid</h3>
                <button
                  className="close-btn"
                  onClick={closeBidModal}
                >
                  ×
                </button>
              </div>

              <div className="modal-content">
                <div className="bid-details">
                  <h4>{selectedBid.content?.title}</h4>
                  <p><strong>Bidder:</strong> {selectedBid.bidder?.firstName} {selectedBid.bidder?.lastName}</p>
                  <p><strong>Email:</strong> {selectedBid.bidder?.email}</p>
                  <p><strong>Bid Amount:</strong> ${selectedBid.amount?.toFixed(2)}</p>
                  <p><strong>Starting Price:</strong> ${selectedBid.content?.auctionDetails?.basePrice?.toFixed(2) || 'N/A'}</p>
                  <p><strong>Auction Status:</strong> {isAuctionActive(selectedBid.content) ? 'Active' : 'Ended'}</p>
                  <p><strong>Bid Date:</strong> {formatDate(selectedBid.createdAt)}</p>
                </div>

                <div className="response-section">
                  <label htmlFor="responseMessage">Your Response (Optional):</label>
                  <textarea
                    id="responseMessage"
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    placeholder="Add a message to the bidder..."
                    rows="3"
                    maxLength="500"
                  />
                  <small>{responseMessage.length}/500</small>
                </div>

                <div className="modal-actions">
                  <button
                    className="btn-reject"
                    onClick={() => handleRejectBid(selectedBid)}
                    disabled={isResponding}
                  >
                    <FaTimes /> {isResponding ? "Processing..." : "Reject Bid"}
                  </button>
                  <button
                    className="btn-accept"
                    onClick={() => handleAcceptBid(selectedBid)}
                    disabled={isResponding}
                  >
                    <FaCheck /> {isResponding ? "Processing..." : "Accept Bid"}
                  </button>
                </div>

                <div className="modal-note">
                  <p><strong>Note:</strong> Accepting this bid will create an order and end the auction for this content.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerBids;
