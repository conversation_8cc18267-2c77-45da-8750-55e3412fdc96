import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { getSellerOffers, updateOfferStatus } from "../../redux/slices/offerSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaEye, FaCheck, FaTimes, FaSync } from "react-icons/fa";
import { MdLocalOffer } from "react-icons/md";
import { toast } from "react-toastify";
import "../../styles/SellerOffers.css";

const SellerOffers = () => {
  const dispatch = useDispatch();
  const { sellerOffers, isLoading, isError, error } = useSelector((state) => state.offer);
  const [processingOfferId, setProcessingOfferId] = useState(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [actionType, setActionType] = useState("");

  useEffect(() => {
    dispatch(getSellerOffers());
  }, [dispatch]);

  const handleOfferAction = (offer, action) => {
    setSelectedOffer(offer);
    setActionType(action);
    setResponseMessage("");
    setShowResponseModal(true);
  };

  const confirmOfferAction = async () => {
    if (!selectedOffer) return;

    setProcessingOfferId(selectedOffer._id);
    try {
      await dispatch(updateOfferStatus({
        offerId: selectedOffer._id,
        status: actionType,
        sellerResponse: responseMessage.trim()
      })).unwrap();

      toast.success(`Offer ${actionType} successfully`);
      dispatch(getSellerOffers()); // Refresh the list
      setShowResponseModal(false);
      setSelectedOffer(null);
      setResponseMessage("");
    } catch (error) {
      toast.error(error.message || `Failed to ${actionType} offer`);
    } finally {
      setProcessingOfferId(null);
    }
  };

  const handleRetry = () => {
    dispatch(getSellerOffers());
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted", 
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired"
    };

    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const columns = [
    {
      header: "Content",
      accessor: "content",
      render: (offer) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {offer.content?.thumbnailUrl ? (
              <img src={offer.content.thumbnailUrl} alt={offer.content.title} />
            ) : (
              <div className="no-thumbnail">
                <MdLocalOffer />
              </div>
            )}
          </div>
          <div className="content-details">
            <h4 className="content-title">{offer.content?.title || "Untitled"}</h4>
            <p className="content-sport">{offer.content?.sport || "N/A"}</p>
          </div>
        </div>
      )
    },
    {
      header: "Buyer",
      accessor: "buyer",
      render: (offer) => (
        <div className="buyer-info">
          <span className="buyer-name">
            {offer.buyer?.firstName} {offer.buyer?.lastName}
          </span>
          <span className="buyer-email">{offer.buyer?.email}</span>
        </div>
      )
    },
    {
      header: "Offer Amount",
      accessor: "amount",
      render: (offer) => (
        <span className="offer-amount">{formatPrice(offer.amount)}</span>
      )
    },
    {
      header: "Message",
      accessor: "message",
      render: (offer) => (
        <div className="offer-message">
          {offer.message ? (
            <span title={offer.message}>
              {offer.message.length > 50 ? `${offer.message.substring(0, 50)}...` : offer.message}
            </span>
          ) : (
            <span className="no-message">No message</span>
          )}
        </div>
      )
    },
    {
      header: "Status",
      accessor: "status",
      render: (offer) => getStatusBadge(offer.status)
    },
    {
      header: "Date",
      accessor: "createdAt",
      render: (offer) => (
        <span className="offer-date">{formatDate(offer.createdAt)}</span>
      )
    },
    {
      header: "Actions",
      accessor: "actions",
      render: (offer) => (
        <div className="action-buttons">
          <Link
            to={`/seller/my-sports-strategies/details/${offer.content?._id}`}
            className="btn-icon btn-view"
            title="View Content"
          >
            <FaEye />
          </Link>
          {offer.status === "Pending" && (
            <>
              <button
                className="btn-icon btn-accept"
                onClick={() => handleOfferAction(offer, "accepted")}
                disabled={processingOfferId === offer._id}
                title="Accept Offer"
              >
                {processingOfferId === offer._id && actionType === "accepted" ? 
                  <FaSync className="spinning" /> : <FaCheck />}
              </button>
              <button
                className="btn-icon btn-reject"
                onClick={() => handleOfferAction(offer, "rejected")}
                disabled={processingOfferId === offer._id}
                title="Reject Offer"
              >
                {processingOfferId === offer._id && actionType === "rejected" ? 
                  <FaSync className="spinning" /> : <FaTimes />}
              </button>
            </>
          )}
        </div>
      )
    }
  ];

  if (isError) {
    return (
      <SellerLayout>
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          title="Failed to load offers"
        />
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="SellerOffers">
        <div className="page-header">
          <h2>Received Offers</h2>
          <p>Manage offers from buyers for your content</p>
        </div>

        {isLoading ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : sellerOffers && sellerOffers.length > 0 ? (
          <>
            <div className="offers-summary">
              <p>You have received {sellerOffers.length} offer{sellerOffers.length !== 1 ? 's' : ''}</p>
            </div>
            <Table
              columns={columns}
              data={sellerOffers}
              className="offers-table"
            />
          </>
        ) : (
          <div className="no-offers">
            <MdLocalOffer className="no-offers-icon" />
            <h3>No Offers Yet</h3>
            <p>You haven't received any offers yet. Keep creating great content to attract buyers!</p>
            <Link to="/seller/my-sports-strategies/add" className="btn-primary">
              Add New Content
            </Link>
          </div>
        )}

        {/* Response Modal */}
        {showResponseModal && (
          <div className="modal-overlay">
            <div className="response-modal">
              <div className="modal-header">
                <h3>{actionType === "accepted" ? "Accept" : "Reject"} Offer</h3>
                <button 
                  className="modal-close"
                  onClick={() => setShowResponseModal(false)}
                >
                  <FaTimes />
                </button>
              </div>
              <div className="modal-body">
                <div className="offer-details">
                  <p><strong>Content:</strong> {selectedOffer?.content?.title}</p>
                  <p><strong>Buyer:</strong> {selectedOffer?.buyer?.firstName} {selectedOffer?.buyer?.lastName}</p>
                  <p><strong>Offer Amount:</strong> {formatPrice(selectedOffer?.amount)}</p>
                  {selectedOffer?.message && (
                    <p><strong>Buyer Message:</strong> {selectedOffer.message}</p>
                  )}
                </div>
                <div className="response-input">
                  <label htmlFor="responseMessage">
                    Response Message {actionType === "rejected" ? "(Required)" : "(Optional)"}
                  </label>
                  <textarea
                    id="responseMessage"
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    placeholder={`Add a ${actionType === "accepted" ? "thank you" : "reason for rejection"} message...`}
                    rows={4}
                    maxLength={500}
                    required={actionType === "rejected"}
                  />
                  <small>{responseMessage.length}/500 characters</small>
                </div>
              </div>
              <div className="modal-actions">
                <button
                  className="btn-secondary"
                  onClick={() => setShowResponseModal(false)}
                  disabled={processingOfferId}
                >
                  Cancel
                </button>
                <button
                  className={`btn-primary ${actionType === "rejected" ? "btn-danger" : ""}`}
                  onClick={confirmOfferAction}
                  disabled={processingOfferId || (actionType === "rejected" && !responseMessage.trim())}
                >
                  {processingOfferId ? "Processing..." : `${actionType === "accepted" ? "Accept" : "Reject"} Offer`}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerOffers;
