import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { createBid, getContentBids } from "../redux/slices/bidSlice";
import { createOffer, getBuyerOffers } from "../redux/slices/offerSlice";
import { getContent } from "../redux/slices/contentSlice";
import BidModal from "../components/common/BidModal";
import OfferModal from "../components/common/OfferModal";
import SellerOfferManagement from "../components/seller/SellerOfferManagement";
import SellerBidManagement from "../components/seller/SellerBidManagement";

const TestAuctionFlow = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const { content } = useSelector((state) => state.content);
  const { contentBids } = useSelector((state) => state.bid);
  const { buyerOffers } = useSelector((state) => state.offer);

  const [selectedContent, setSelectedContent] = useState(null);
  const [isBidModalOpen, setIsBidModalOpen] = useState(false);
  const [isOfferModalOpen, setIsOfferModalOpen] = useState(false);
  const [testContentId, setTestContentId] = useState("");

  // Mock content for testing
  const mockAuctionContent = {
    _id: "test-auction-content",
    title: "Test Auction Strategy",
    description: "This is a test auction content for testing the auction flow",
    price: 50.00,
    saleType: "Auction",
    auctionDetails: {
      basePrice: 25.00,
      auctionStartDate: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
      auctionEndDate: new Date(Date.now() + 1000 * 60 * 60 * 24), // 24 hours from now
      minimumBidIncrement: 5.00,
      allowOfferBeforeAuctionStart: true
    },
    seller: {
      _id: "test-seller",
      firstName: "Test",
      lastName: "Seller"
    },
    thumbnailUrl: "https://via.placeholder.com/300x200"
  };

  const mockOfferContent = {
    _id: "test-offer-content",
    title: "Test Offer Strategy",
    description: "This is a test content for testing the offer flow",
    price: 30.00,
    saleType: "Auction",
    auctionDetails: {
      basePrice: 20.00,
      auctionStartDate: new Date(Date.now() + 1000 * 60 * 60 * 2), // 2 hours from now
      auctionEndDate: new Date(Date.now() + 1000 * 60 * 60 * 26), // 26 hours from now
      minimumBidIncrement: 2.50,
      allowOfferBeforeAuctionStart: true
    },
    seller: {
      _id: "test-seller",
      firstName: "Test",
      lastName: "Seller"
    },
    thumbnailUrl: "https://via.placeholder.com/300x200"
  };

  const loadTestContent = async () => {
    if (testContentId) {
      try {
        await dispatch(getContent(testContentId)).unwrap();
        toast.success("Content loaded successfully!");
      } catch (error) {
        toast.error("Failed to load content: " + error.message);
      }
    }
  };

  const testBidFlow = () => {
    setSelectedContent(mockAuctionContent);
    setIsBidModalOpen(true);
  };

  const testOfferFlow = () => {
    setSelectedContent(mockOfferContent);
    setIsOfferModalOpen(true);
  };

  const loadBids = async () => {
    if (selectedContent) {
      try {
        await dispatch(getContentBids(selectedContent._id)).unwrap();
        toast.success("Bids loaded successfully!");
      } catch (error) {
        toast.error("Failed to load bids: " + error.message);
      }
    }
  };

  const loadOffers = async () => {
    try {
      await dispatch(getBuyerOffers()).unwrap();
      toast.success("Offers loaded successfully!");
    } catch (error) {
      toast.error("Failed to load offers: " + error.message);
    }
  };

  const getAuctionStatus = (content) => {
    if (!content || content.saleType !== "Auction") {
      return "Not an auction";
    }

    const now = new Date();
    const startDate = content.auctionDetails?.auctionStartDate ? new Date(content.auctionDetails.auctionStartDate) : null;
    const endDate = content.auctionDetails?.auctionEndDate ? new Date(content.auctionDetails.auctionEndDate) : null;

    if (startDate && now < startDate) {
      return content.auctionDetails?.allowOfferBeforeAuctionStart ? "Pre-auction (offers allowed)" : "Pre-auction (no offers)";
    }

    if (startDate && endDate && now >= startDate && now <= endDate) {
      return "Active auction";
    }

    if (endDate && now > endDate) {
      return "Auction ended";
    }

    return "Unknown status";
  };

  return (
    <div style={{ padding: "24px", maxWidth: "1200px", margin: "0 auto" }}>
      <h1>Auction Flow Test Page</h1>
      <p>Current user: {user?.firstName} {user?.lastName} ({user?.role})</p>

      {/* Test Content Loading */}
      <div style={{ marginBottom: "32px", padding: "16px", border: "1px solid #e5e7eb", borderRadius: "8px" }}>
        <h2>Test Real Content</h2>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <input
            type="text"
            placeholder="Enter content ID"
            value={testContentId}
            onChange={(e) => setTestContentId(e.target.value)}
            style={{ padding: "8px", border: "1px solid #d1d5db", borderRadius: "4px", flex: 1 }}
          />
          <button
            onClick={loadTestContent}
            style={{ padding: "8px 16px", backgroundColor: "#3b82f6", color: "white", border: "none", borderRadius: "4px" }}
          >
            Load Content
          </button>
        </div>
        {content && (
          <div style={{ marginTop: "16px", padding: "12px", backgroundColor: "#f9fafb", borderRadius: "6px" }}>
            <h3>{content.title}</h3>
            <p>Sale Type: {content.saleType}</p>
            <p>Status: {getAuctionStatus(content)}</p>
            {content.auctionDetails && (
              <div>
                <p>Base Price: ${content.auctionDetails.basePrice?.toFixed(2)}</p>
                <p>Start: {content.auctionDetails.auctionStartDate ? new Date(content.auctionDetails.auctionStartDate).toLocaleString() : "Not set"}</p>
                <p>End: {content.auctionDetails.auctionEndDate ? new Date(content.auctionDetails.auctionEndDate).toLocaleString() : "Not set"}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Test Mock Auction Flow */}
      <div style={{ marginBottom: "32px", padding: "16px", border: "1px solid #e5e7eb", borderRadius: "8px" }}>
        <h2>Test Mock Auction Flow</h2>
        <div style={{ display: "flex", gap: "12px", marginBottom: "16px" }}>
          <button
            onClick={testBidFlow}
            style={{ padding: "8px 16px", backgroundColor: "#f59e0b", color: "white", border: "none", borderRadius: "4px" }}
          >
            Test Bid Flow (Active Auction)
          </button>
          <button
            onClick={testOfferFlow}
            style={{ padding: "8px 16px", backgroundColor: "#10b981", color: "white", border: "none", borderRadius: "4px" }}
          >
            Test Offer Flow (Pre-Auction)
          </button>
          <button
            onClick={loadBids}
            style={{ padding: "8px 16px", backgroundColor: "#6b7280", color: "white", border: "none", borderRadius: "4px" }}
          >
            Load Bids
          </button>
          <button
            onClick={loadOffers}
            style={{ padding: "8px 16px", backgroundColor: "#6b7280", color: "white", border: "none", borderRadius: "4px" }}
          >
            Load Offers
          </button>
        </div>

        {/* Display loaded data */}
        {contentBids.length > 0 && (
          <div style={{ marginBottom: "16px" }}>
            <h3>Content Bids ({contentBids.length})</h3>
            <ul>
              {contentBids.map((bid, index) => (
                <li key={index}>
                  ${bid.amount?.toFixed(2)} by {bid.bidder?.firstName} - {bid.status}
                </li>
              ))}
            </ul>
          </div>
        )}

        {buyerOffers.length > 0 && (
          <div>
            <h3>Buyer Offers ({buyerOffers.length})</h3>
            <ul>
              {buyerOffers.map((offer, index) => (
                <li key={index}>
                  ${offer.amount?.toFixed(2)} for {offer.content?.title} - {offer.status}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Seller Management Components */}
      {user?.role === "seller" && (
        <div style={{ marginBottom: "32px" }}>
          <h2>Seller Management</h2>
          <div style={{ display: "grid", gap: "24px" }}>
            <SellerOfferManagement />
            <SellerBidManagement />
          </div>
        </div>
      )}

      {/* API Test Results */}
      <div style={{ padding: "16px", backgroundColor: "#f3f4f6", borderRadius: "8px" }}>
        <h3>API Test Instructions</h3>
        <ol>
          <li>Create auction content with future start date and enable offers</li>
          <li>Test "Make Offer" button before auction starts</li>
          <li>Update auction start date to current time</li>
          <li>Test "Bid Now" button during auction</li>
          <li>As seller, review and accept/reject offers and bids</li>
          <li>Verify order creation and download access</li>
        </ol>
      </div>

      {/* Modals */}
      {selectedContent && (
        <>
          <BidModal
            isOpen={isBidModalOpen}
            onClose={() => setIsBidModalOpen(false)}
            strategy={selectedContent}
          />
          <OfferModal
            isOpen={isOfferModalOpen}
            onClose={() => setIsOfferModalOpen(false)}
            content={selectedContent}
          />
        </>
      )}
    </div>
  );
};

export default TestAuctionFlow;
