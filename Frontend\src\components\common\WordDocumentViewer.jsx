import React, { useState, useEffect } from 'react';
import { FaDownload, FaExclamationTriangle, FaSync, FaFileWord } from 'react-icons/fa';
import { API_BASE_URL } from '../../utils/constants';
import '../../styles/WordDocumentViewer.css';

const WordDocumentViewer = ({ 
  fileUrl, 
  fileName = '',
  title = 'Word Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [documentData, setDocumentData] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Extract filename from fileUrl for API call
  const getFileName = () => {
    if (fileName) return fileName;
    if (fileUrl) return fileUrl.split('/').pop();
    return 'document.docx';
  };

  // Load document preview
  useEffect(() => {
    const loadDocumentPreview = async () => {
      if (!fileUrl) {
        setHasError(true);
        setErrorMessage('No file URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);
        
        const actualFileName = getFileName();
        console.log(`[WordViewer] Loading preview for: ${actualFileName}`);
        
        // Call our custom document preview API
        const response = await fetch(`${API_BASE_URL}/document-preview/convert`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            fileUrl: fileUrl,
            fileName: actualFileName
          })
        });

        if (!response.ok) {
          throw new Error(`Preview generation failed: ${response.status}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.message || 'Failed to generate preview');
        }

        console.log(`[WordViewer] Preview loaded successfully`);
        setDocumentData(result.data);
        setIsLoading(false);
        
      } catch (error) {
        console.error('[WordViewer] Preview loading failed:', error);
        setHasError(true);
        setErrorMessage(error.message);
        setIsLoading(false);
      }
    };

    loadDocumentPreview();
  }, [fileUrl, fileName]);

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className={`word-document-viewer ${className}`} style={{ height }}>
        <div className="word-document-viewer__header">
          <div className="word-document-viewer__info">
            <span className="word-document-viewer__title">{title}</span>
            <span className="word-document-viewer__type">Microsoft Word Document</span>
          </div>
          {showDownload && (
            <button 
              className="word-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )}
        </div>
        
        <div className="word-document-viewer__loading">
          <FaSync className="spinning" />
          <p>Converting Word document...</p>
          <p className="word-document-viewer__loading-info">
            This may take a moment for large documents
          </p>
        </div>
      </div>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <div className={`word-document-viewer ${className}`} style={{ height }}>
        <div className="word-document-viewer__header">
          <div className="word-document-viewer__info">
            <span className="word-document-viewer__title">{title}</span>
            <span className="word-document-viewer__type">Microsoft Word Document</span>
          </div>
          {showDownload && (
            <button 
              className="word-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )}
        </div>
        
        <div className="word-document-viewer__error">
          <FaExclamationTriangle />
          <h3>Preview Not Available</h3>
          <p>Unable to generate preview for this Word document.</p>
          <p className="word-document-viewer__error-details">{errorMessage}</p>
          
          {showDownload && (
            <button 
              className="word-document-viewer__download-button"
              onClick={handleDownload}
            >
              <FaDownload />
              Download Word Document
            </button>
          )}
        </div>
      </div>
    );
  }

  // Render document content
  return (
    <div className={`word-document-viewer ${className}`} style={{ height }}>
      <div className="word-document-viewer__header">
        <div className="word-document-viewer__info">
          <span className="word-document-viewer__title">{title}</span>
          <span className="word-document-viewer__type">Microsoft Word Document</span>
          {documentData?.metadata && (
            <span className="word-document-viewer__stats">
              {documentData.metadata.wordCount} words • {documentData.metadata.charCount} characters
            </span>
          )}
        </div>
        
        {showDownload && (
          <button 
            className="word-document-viewer__download-btn"
            onClick={handleDownload}
            title="Download Document"
          >
            <FaDownload />
          </button>
        )}
      </div>

      <div className="word-document-viewer__content">
        {documentData?.html ? (
          <div 
            className="word-document-viewer__html-content"
            dangerouslySetInnerHTML={{ __html: documentData.html }}
          />
        ) : (
          <div className="word-document-viewer__no-content">
            <FaFileWord />
            <p>Document content could not be displayed</p>
            <p>Please download the file to view the full content</p>
          </div>
        )}
      </div>

      {/* Metadata footer */}
      {documentData?.metadata && (
        <div className="word-document-viewer__footer">
          <div className="word-document-viewer__metadata">
            {documentData.metadata.hasImages && (
              <span className="word-document-viewer__meta-item">Contains Images</span>
            )}
            {documentData.metadata.warnings && documentData.metadata.warnings.length > 0 && (
              <span className="word-document-viewer__meta-item">
                {documentData.metadata.warnings.length} conversion warnings
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WordDocumentViewer;
