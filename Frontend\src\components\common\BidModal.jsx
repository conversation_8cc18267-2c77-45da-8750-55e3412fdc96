import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaTimes } from "react-icons/fa";
import { toast } from "react-toastify";
import thankyouIcon from "../../assets/images/thankyou.svg";
import { createBid, getContentBids } from "../../redux/slices/bidSlice";
import "../../styles/BidModal.css";

const BidModal = ({ isOpen, onClose, strategy }) => {
  const dispatch = useDispatch();
  const { isLoading, contentBids } = useSelector((state) => state.bid);

  const [bidAmount, setBidAmount] = useState("");
  const [showSuccessState, setShowSuccessState] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 1,
    minutes: 59,
    seconds: 30
  });

  // Calculate auction end time
  const getAuctionEndTime = () => {
    if (strategy?.auctionDetails?.auctionEndDate) {
      return new Date(strategy.auctionDetails.auctionEndDate);
    }
    // Fallback to a default time if no end date
    const defaultEnd = new Date();
    defaultEnd.setHours(defaultEnd.getHours() + 2); // 2 hours from now
    return defaultEnd;
  };

  // Load content bids when modal opens
  useEffect(() => {
    if (isOpen && strategy?._id) {
      dispatch(getContentBids(strategy._id));
    }
  }, [isOpen, strategy?._id, dispatch]);

  // Format bid history from real data
  const bidHistory = contentBids?.map((bid, index) => ({
    no: index + 1,
    bidId: `#${bid._id.slice(-6)}`,
    date: new Date(bid.createdAt).toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }),
    bidAmount: `$${bid.amount.toFixed(2)}`,
    bidder: bid.bidder?.firstName || 'Anonymous'
  })) || [];

  // Define handleCloseModal first
  const handleCloseModal = useCallback(() => {
    setShowSuccessState(false);
    setBidAmount("");
    setIsSubmitting(false);
    onClose();
  }, [onClose]);

  // Countdown timer effect
  useEffect(() => {
    if (!isOpen) return;

    const updateCountdown = () => {
      const now = new Date();
      const endTime = getAuctionEndTime();
      const timeDiff = endTime - now;

      if (timeDiff <= 0) {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        return;
      }

      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      setTimeLeft({ days, hours, minutes, seconds });
    };

    updateCountdown(); // Initial call
    const timer = setInterval(updateCountdown, 1000);

    return () => clearInterval(timer);
  }, [isOpen, strategy]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        handleCloseModal();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, handleCloseModal]);

  // Auto-close success state after 3 seconds
  useEffect(() => {
    if (showSuccessState) {
      const timer = setTimeout(() => {
        handleCloseModal();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [showSuccessState, handleCloseModal]);

  const handleSubmitBid = async (e) => {
    e.preventDefault();

    if (!bidAmount || parseFloat(bidAmount) <= 0) {
      toast.error("Please enter a valid bid amount");
      return;
    }

    // Validate minimum bid amount
    const basePrice = strategy?.auctionDetails?.basePrice || strategy?.price || 0;
    const minIncrement = strategy?.auctionDetails?.minimumBidIncrement || 1;
    const highestBid = bidHistory.length > 0 ? Math.max(...bidHistory.map(b => parseFloat(b.bidAmount.replace('$', '')))) : 0;
    const minBidAmount = Math.max(basePrice, highestBid + minIncrement);

    if (parseFloat(bidAmount) < minBidAmount) {
      toast.error(`Bid must be at least $${minBidAmount.toFixed(2)}`);
      return;
    }

    setIsSubmitting(true);

    try {
      const bidData = {
        contentId: strategy._id,
        amount: parseFloat(bidAmount)
      };

      await dispatch(createBid(bidData)).unwrap();

      // Refresh bids list
      dispatch(getContentBids(strategy._id));

      setShowSuccessState(true);
      toast.success("Bid submitted successfully! Your previous bid (if any) has been updated.");

      // Auto-close after 3 seconds
      setTimeout(() => {
        handleCloseModal();
      }, 3000);

    } catch (error) {
      console.error("Error submitting bid:", error);
      toast.error(error.message || "Failed to submit bid. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="bid-modal-overlay" onClick={handleOverlayClick}>
      <div className={`bid-modal ${showSuccessState ? 'bid-modal--success' : ''}`}>
        <div className="bid-modal__header">
          <h2 className="bid-modal__title">
            {showSuccessState ? "Your bid is submitted successfully!" : "Bid is Available Now!"}
          </h2>
          <button className="bid-modal__close" onClick={handleCloseModal}>
            <FaTimes />
          </button>
        </div>

        <div className="bid-modal__content">
          {showSuccessState ? (
            /* Success State */
            <div className="bid-modal__success">
              <div className="success-icon">
                <img src={thankyouIcon} alt="Success" />
              </div>
              <p className="success-message">
                Thank you for your bid! We will notify you about the bidding results.
              </p>
            </div>
          ) : (
            <>
              {/* Countdown Timer */}
              <div className="bid-modal__countdown">
                <div className="countdown-item">
                  <span className="countdown-number">{String(timeLeft.days).padStart(2, '0')}</span>
                  <span className="countdown-label">DAYS</span>
                </div>
                <div className="countdown-item">
                  <span className="countdown-number">{String(timeLeft.hours).padStart(2, '0')}</span>
                  <span className="countdown-label">HOURS</span>
                </div>
                <div className="countdown-item">
                  <span className="countdown-number">{String(timeLeft.minutes).padStart(2, '0')}</span>
                  <span className="countdown-label">MINUTES</span>
                </div>
                <div className="countdown-item">
                  <span className="countdown-number">{String(timeLeft.seconds).padStart(2, '0')}</span>
                  <span className="countdown-label">SECONDS</span>
                </div>
              </div>

              {/* Bid Form */}
              <div className="bid-modal__form-section">
                <h3 className="bid-modal__form-title">Enter Your Bid Amount</h3>
                <form onSubmit={handleSubmitBid} className="bid-modal__form">
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    placeholder="Enter Bid Amount"
                    className="bid-modal__input"
                    required
                  />
                  <button
                    type="submit"
                    className="bid-modal__submit-btn"
                    disabled={isSubmitting || isLoading}
                  >
                    {isSubmitting ? "SUBMITTING..." : "SUBMIT BID"}
                  </button>
                </form>
              </div>

              {/* Others Bid Section */}
              <div className="bid-modal__history-section">
                <h3 className="bid-modal__history-title">Others Bid</h3>
                <div className="bid-modal__table-container">
                  <table className="bid-modal__table">
                    <thead>
                      <tr>
                        <th>No.</th>
                        <th>Bid Id</th>
                        <th>Date</th>
                        <th>Bid Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {bidHistory.map((bid) => (
                        <tr key={bid.no}>
                          <td>{bid.no}</td>
                          <td>{bid.bidId}</td>
                          <td>{bid.date}</td>
                          <td>{bid.bidAmount}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BidModal;
