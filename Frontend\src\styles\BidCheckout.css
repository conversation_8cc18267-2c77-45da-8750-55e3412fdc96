.bid-checkout {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 24px;
}

.checkout-header {
  max-width: 800px;
  margin: 0 auto 32px;
  text-align: center;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  font-size: 0.875rem;
  margin-bottom: 24px;
  transition: all 0.2s;
}

.back-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.checkout-header h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
}

.checkout-header p {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
}

.checkout-container {
  max-width: 800px;
  margin: 0 auto;
}

.checkout-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-preview {
  display: flex;
  gap: 20px;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.content-image {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.content-details {
  flex: 1;
}

.content-details h2 {
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.content-details p {
  margin: 4px 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.content-type {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
  margin-top: 8px;
}

.checkout-form {
  padding: 24px;
}

.payment-section {
  margin-bottom: 32px;
}

.payment-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.card-element-container {
  padding: 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  margin-bottom: 16px;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #059669;
  font-size: 0.875rem;
  font-weight: 500;
}

.security-icon {
  color: #059669;
}

.order-summary {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.order-summary h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.875rem;
}

.summary-item:first-of-type span:last-child {
  font-weight: 500;
  color: #111827;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.pay-button {
  width: 100%;
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pay-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.pay-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Stripe Element Styling */
.StripeElement {
  height: 40px;
  padding: 10px 12px;
  color: #32325d;
  background-color: white;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

/* Loading States */
.checkout-form.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Error States */
.payment-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 0.875rem;
}

/* Success States */
.payment-success {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bid-checkout {
    padding: 16px;
  }
  
  .content-preview {
    flex-direction: column;
    gap: 16px;
  }
  
  .content-image {
    width: 100%;
    height: 120px;
  }
  
  .checkout-form {
    padding: 20px;
  }
  
  .summary-item {
    font-size: 0.8rem;
  }
  
  .summary-total {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .checkout-header h1 {
    font-size: 1.5rem;
  }
  
  .checkout-header p {
    font-size: 1rem;
  }
  
  .pay-button {
    padding: 14px 20px;
    font-size: 0.9rem;
  }
}
