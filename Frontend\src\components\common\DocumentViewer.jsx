import React from 'react';
import { FaFileWord, FaFileExcel, FaFilePowerpoint, FaFileAlt, FaFilePdf, FaFile, FaDownload } from 'react-icons/fa';
import SimplePDFViewer from './SimplePDFViewer';
import OfficeDocumentViewer from './OfficeDocumentViewer';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({ 
  fileUrl, 
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  // Get file extension
  const getFileExtension = (filename) => {
    if (!filename) return '';
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    return filename.substring(lastDotIndex).toLowerCase();
  };

  // Get document type from file extension
  const getDocumentType = (filename) => {
    const extension = getFileExtension(filename);

    // Microsoft Word
    if (['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'].includes(extension)) {
      return 'word';
    }

    // Microsoft Excel
    if (['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'].includes(extension)) {
      return 'excel';
    }

    // Microsoft PowerPoint
    if (['.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm'].includes(extension)) {
      return 'powerpoint';
    }

    // PDF
    if (extension === '.pdf') {
      return 'pdf';
    }

    // Text documents
    if (['.txt', '.rtf'].includes(extension)) {
      return 'text';
    }

    // OpenDocument
    if (['.odt', '.ods', '.odp', '.odg', '.odf'].includes(extension)) {
      return 'opendocument';
    }

    // Apple iWork
    if (['.pages', '.numbers', '.key'].includes(extension)) {
      return 'iwork';
    }

    // E-books
    if (['.epub', '.mobi', '.azw', '.azw3'].includes(extension)) {
      return 'ebook';
    }

    return 'unknown';
  };

  // Get icon for document type
  const getDocumentIcon = (type) => {
    switch (type) {
      case 'word':
        return <FaFileWord className="document-viewer__icon document-viewer__icon--word" />;
      case 'excel':
        return <FaFileExcel className="document-viewer__icon document-viewer__icon--excel" />;
      case 'powerpoint':
        return <FaFilePowerpoint className="document-viewer__icon document-viewer__icon--powerpoint" />;
      case 'pdf':
        return <FaFilePdf className="document-viewer__icon document-viewer__icon--pdf" />;
      case 'text':
      case 'opendocument':
      case 'iwork':
      case 'ebook':
        return <FaFileAlt className="document-viewer__icon document-viewer__icon--text" />;
      default:
        return <FaFile className="document-viewer__icon document-viewer__icon--default" />;
    }
  };

  // Get document type name
  const getDocumentTypeName = (type) => {
    switch (type) {
      case 'word':
        return 'Microsoft Word Document';
      case 'excel':
        return 'Microsoft Excel Spreadsheet';
      case 'powerpoint':
        return 'Microsoft PowerPoint Presentation';
      case 'pdf':
        return 'PDF Document';
      case 'text':
        return 'Text Document';
      case 'opendocument':
        return 'OpenDocument Format';
      case 'iwork':
        return 'Apple iWork Document';
      case 'ebook':
        return 'E-book';
      default:
        return 'Document';
    }
  };

  const documentType = getDocumentType(fileName);
  const extension = getFileExtension(fileName);

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  // For PDF files, use the PDF viewer
  if (documentType === 'pdf') {
    return (
      <div className={`document-viewer ${className}`} style={{ height }}>
        <SimplePDFViewer
          fileUrl={fileUrl}
          title={title}
          height="100%"
        />
        {showDownload && (
          <div className="document-viewer__download-overlay">
            <button
              className="document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          </div>
        )}
      </div>
    );
  }

  // For Office documents (Word, Excel, PowerPoint), use the Office viewer
  if (['word', 'excel', 'powerpoint'].includes(documentType)) {
    return (
      <OfficeDocumentViewer
        fileUrl={fileUrl}
        fileName={fileName}
        title={title}
        documentType={documentType}
        className={className}
        height={height}
        showDownload={showDownload}
        onDownload={onDownload}
      />
    );
  }

  // For other document types, show preview card
  return (
    <div className={`document-viewer ${className}`} style={{ height }}>
      <div className="document-viewer__preview-card">
        <div className="document-viewer__header">
          <div className="document-viewer__icon-container">
            {getDocumentIcon(documentType)}
          </div>
          <div className="document-viewer__info">
            <h3 className="document-viewer__title">{title}</h3>
            <p className="document-viewer__type">{getDocumentTypeName(documentType)}</p>
            <p className="document-viewer__filename">{fileName}</p>
            <p className="document-viewer__extension">{extension.toUpperCase()} File</p>
          </div>
        </div>
        
        <div className="document-viewer__content">
          <div className="document-viewer__preview-message">
            <p>This document type cannot be previewed directly in the browser.</p>
            <p>Download the file to view its contents.</p>
          </div>
          
          {showDownload && (
            <button 
              className="document-viewer__download-button"
              onClick={handleDownload}
            >
              <FaDownload />
              Download {getDocumentTypeName(documentType)}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
