/* Auction Bid Acceptance Modal Styles */

.auction-bid-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-icon {
  color: #f59e0b;
  font-size: 1.25rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 24px;
}

/* Bid Details Section */
.bid-details-section {
  margin-bottom: 24px;
}

.bid-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.bid-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.bid-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bid-info-item .label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.bid-info-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.bid-amount {
  color: #059669 !important;
  font-size: 1.25rem !important;
}

.status-active {
  color: #059669 !important;
}

.status-won {
  color: #dc2626 !important;
}

.status-lost {
  color: #6b7280 !important;
}

/* Content Details Section */
.content-details-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.content-details-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.content-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.content-meta {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Warning Section */
.warning-section {
  margin-bottom: 24px;
}

.warning-box {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
}

.warning-icon {
  color: #d97706;
  font-size: 1.25rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #92400e;
  margin: 0 0 8px 0;
}

.warning-content ul {
  margin: 0;
  padding-left: 16px;
  color: #92400e;
}

.warning-content li {
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.warning-content strong {
  font-weight: 600;
}

/* Response Section */
.response-section {
  margin-bottom: 24px;
}

.response-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.response-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.response-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.character-count {
  text-align: right;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

/* Earnings Section */
.earnings-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
}

.earnings-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 16px 0;
}

.earnings-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.earnings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.earnings-item.total {
  padding-top: 8px;
  border-top: 1px solid #0ea5e9;
  font-weight: 600;
}

.earnings-item .label {
  font-size: 0.875rem;
  color: #0c4a6e;
}

.earnings-item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #0c4a6e;
}

.earnings-item.total .value {
  color: #059669;
  font-size: 1.125rem;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px 24px;
  border-top: 1px solid #e5e7eb;
}

.modal-actions button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-danger {
  background: #dc2626;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

.btn-success {
  background: #059669;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #047857;
}

.modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 640px) {
  .auction-bid-modal {
    width: 95%;
    margin: 20px;
  }
  
  .bid-info-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions button {
    width: 100%;
    justify-content: center;
  }
}
