const mongoose = require("mongoose");

const OfferSchema = new mongoose.Schema({
  content: {
    type: mongoose.Schema.ObjectId,
    ref: "Content",
    required: true,
  },
  buyer: {
    type: mongoose.Schema.ObjectId,
    ref: "User",
    required: true,
  },
  seller: {
    type: mongoose.Schema.ObjectId,
    ref: "User",
    required: true,
  },
  amount: {
    type: Number,
    required: [true, "Please add an offer amount"],
    min: [0, "Offer amount cannot be negative"],
  },
  status: {
    type: String,
    enum: ["Pending", "Accepted", "Rejected", "Cancelled", "Expired"],
    default: "Pending",
  },
  message: {
    type: String,
    maxlength: [500, "Message cannot be more than 500 characters"],
    trim: true,
  },
  sellerResponse: {
    type: String,
    maxlength: [500, "Response cannot be more than 500 characters"],
    trim: true,
  },
  expiresAt: {
    type: Date,
    default: function () {
      // Default expiry: 7 days from creation
      return new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    },
  },
  acceptedAt: {
    type: Date,
  },
  rejectedAt: {
    type: Date,
  },
  orderId: {
    type: mongoose.Schema.ObjectId,
    ref: "Order",
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Prevent user from submitting more than one pending offer per content
OfferSchema.index(
  { content: 1, buyer: 1, status: 1 },
  {
    unique: true,
    partialFilterExpression: { status: "Pending" },
  }
);

// Index for efficient queries
OfferSchema.index({ seller: 1, status: 1 });
OfferSchema.index({ buyer: 1, status: 1 });
OfferSchema.index({ content: 1, status: 1 });
OfferSchema.index({ expiresAt: 1 });

// Pre-save middleware to set seller from content
OfferSchema.pre("save", async function (next) {
  if (this.isNew && !this.seller) {
    try {
      const Content = mongoose.model("Content");
      const content = await Content.findById(this.content);
      if (content && content.seller) {
        this.seller = content.seller;
      } else {
        return next(new Error("Content not found or seller not specified"));
      }
    } catch (error) {
      return next(error);
    }
  }
  next();
});

// Method to check if offer is expired
OfferSchema.methods.isExpired = function () {
  return this.expiresAt < new Date();
};

// Method to accept offer
OfferSchema.methods.accept = function (sellerResponse) {
  this.status = "Accepted";
  this.acceptedAt = new Date();
  if (sellerResponse) {
    this.sellerResponse = sellerResponse;
  }
  return this.save();
};

// Method to reject offer
OfferSchema.methods.reject = function (sellerResponse) {
  this.status = "Rejected";
  this.rejectedAt = new Date();
  if (sellerResponse) {
    this.sellerResponse = sellerResponse;
  }
  return this.save();
};

module.exports = mongoose.model("Offer", OfferSchema);
