/**
 * Test Script for Auction Bid Acceptance Functionality
 * 
 * This script tests the complete auction bid acceptance flow:
 * 1. Create auction content
 * 2. Place multiple bids
 * 3. Accept a bid
 * 4. Verify auction ends and content is hidden
 * 5. Verify email notification
 * 6. Test checkout process
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:5173';

// Test user credentials (you'll need to create these users first)
const SELLER_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

const BUYER1_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

const BUYER2_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

let sellerToken, buyer1Token, buyer2Token;
let contentId, bid1Id, bid2Id, orderId;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null, token = null) => {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: token ? { Authorization: `Bearer ${token}` } : {},
    data
  };

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Step 1: Login users
const loginUsers = async () => {
  console.log('🔐 Logging in users...');
  
  try {
    const sellerLogin = await makeRequest('POST', '/auth/login', SELLER_CREDENTIALS);
    sellerToken = sellerLogin.token;
    console.log('✅ Seller logged in');

    const buyer1Login = await makeRequest('POST', '/auth/login', BUYER1_CREDENTIALS);
    buyer1Token = buyer1Login.token;
    console.log('✅ Buyer 1 logged in');

    const buyer2Login = await makeRequest('POST', '/auth/login', BUYER2_CREDENTIALS);
    buyer2Token = buyer2Login.token;
    console.log('✅ Buyer 2 logged in');
  } catch (error) {
    console.error('❌ Failed to login users');
    throw error;
  }
};

// Step 2: Create auction content
const createAuctionContent = async () => {
  console.log('\n📝 Creating auction content...');
  
  const contentData = {
    title: 'Test Auction Strategy - Basketball Fundamentals',
    description: 'This is a test auction content for testing bid acceptance functionality',
    sport: 'Basketball',
    contentType: 'Video',
    category: 'Training',
    difficulty: 'Intermediate',
    coachName: 'Test Coach',
    aboutCoach: 'Experienced basketball coach',
    strategicContent: 'Advanced basketball strategies and techniques',
    saleType: 'Auction',
    auctionDetails: {
      basePrice: 25.00,
      startingBid: 25.00,
      minimumBidIncrement: 5.00,
      auctionStartDate: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
      auctionEndDate: new Date(Date.now() + 1000 * 60 * 60 * 24), // 24 hours from now
      allowOfferBeforeAuctionStart: false
    },
    fileUrl: 'https://example.com/test-video.mp4',
    thumbnailUrl: 'https://example.com/test-thumbnail.jpg'
  };

  try {
    const response = await makeRequest('POST', '/content', contentData, sellerToken);
    contentId = response.data._id;
    console.log(`✅ Auction content created with ID: ${contentId}`);
  } catch (error) {
    console.error('❌ Failed to create auction content');
    throw error;
  }
};

// Step 3: Place multiple bids
const placeBids = async () => {
  console.log('\n💰 Placing bids...');
  
  try {
    // Buyer 1 places first bid
    const bid1Response = await makeRequest('POST', '/bids', {
      contentId: contentId,
      amount: 30.00
    }, buyer1Token);
    bid1Id = bid1Response.data._id;
    console.log(`✅ Buyer 1 placed bid: $30.00 (ID: ${bid1Id})`);

    // Buyer 2 places higher bid
    const bid2Response = await makeRequest('POST', '/bids', {
      contentId: contentId,
      amount: 40.00
    }, buyer2Token);
    bid2Id = bid2Response.data._id;
    console.log(`✅ Buyer 2 placed bid: $40.00 (ID: ${bid2Id})`);

    // Buyer 1 places even higher bid
    const bid3Response = await makeRequest('POST', '/bids', {
      contentId: contentId,
      amount: 50.00
    }, buyer1Token);
    console.log(`✅ Buyer 1 placed higher bid: $50.00 (ID: ${bid3Response.data._id})`);
    
    // Update bid1Id to the latest bid from buyer 1
    bid1Id = bid3Response.data._id;
  } catch (error) {
    console.error('❌ Failed to place bids');
    throw error;
  }
};

// Step 4: Accept a bid (this should end the auction)
const acceptBid = async () => {
  console.log('\n🎯 Accepting bid...');
  
  try {
    const response = await makeRequest('PUT', `/bids/${bid1Id}/status`, {
      status: 'accepted',
      sellerResponse: 'Great bid! Auction ended.'
    }, sellerToken);
    
    orderId = response.data.orderId;
    console.log(`✅ Bid accepted! Order created with ID: ${orderId}`);
    console.log(`✅ Auction ended: ${response.data.auctionEnded}`);
  } catch (error) {
    console.error('❌ Failed to accept bid');
    throw error;
  }
};

// Step 5: Verify auction status and content visibility
const verifyAuctionStatus = async () => {
  console.log('\n🔍 Verifying auction status...');
  
  try {
    // Check content details
    const contentResponse = await makeRequest('GET', `/content/${contentId}`);
    const content = contentResponse.data;
    
    console.log(`✅ Content auction status: ${content.auctionStatus}`);
    console.log(`✅ Content auction ended at: ${content.auctionEndedAt}`);
    console.log(`✅ Winning bid ID: ${content.winningBidId}`);
    
    // Check if content appears in public listing
    const publicContentResponse = await makeRequest('GET', '/content');
    const isContentVisible = publicContentResponse.data.some(item => item._id === contentId);
    
    if (isContentVisible) {
      console.log('⚠️  WARNING: Content is still visible in public listing');
    } else {
      console.log('✅ Content correctly hidden from public listing');
    }
    
    // Check bid statuses
    const bidsResponse = await makeRequest('GET', `/bids/content/${contentId}`);
    const bids = bidsResponse.data;
    
    console.log('\n📊 Bid statuses:');
    bids.forEach(bid => {
      console.log(`   - Bid ${bid._id}: $${bid.amount} - Status: ${bid.status}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to verify auction status');
    throw error;
  }
};

// Step 6: Test checkout process
const testCheckout = async () => {
  console.log('\n💳 Testing checkout process...');
  
  try {
    // Get order details
    const orderResponse = await makeRequest('GET', `/orders/${orderId}`, null, buyer1Token);
    const order = orderResponse.data;
    
    console.log(`✅ Order details retrieved:`);
    console.log(`   - Order ID: ${order._id}`);
    console.log(`   - Amount: $${order.amount}`);
    console.log(`   - Status: ${order.status}`);
    console.log(`   - Payment Status: ${order.paymentStatus}`);
    console.log(`   - Order Type: ${order.orderType}`);
    
    console.log(`\n🔗 Checkout URL: ${FRONTEND_URL}/checkout/${orderId}`);
    
  } catch (error) {
    console.error('❌ Failed to test checkout process');
    throw error;
  }
};

// Main test function
const runTests = async () => {
  console.log('🚀 Starting Auction Bid Acceptance Tests\n');
  
  try {
    await loginUsers();
    await createAuctionContent();
    await placeBids();
    await acceptBid();
    await verifyAuctionStatus();
    await testCheckout();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log(`   - Content ID: ${contentId}`);
    console.log(`   - Winning Bid ID: ${bid1Id}`);
    console.log(`   - Order ID: ${orderId}`);
    console.log(`   - Checkout URL: ${FRONTEND_URL}/checkout/${orderId}`);
    
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
};

// Run the tests
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  loginUsers,
  createAuctionContent,
  placeBids,
  acceptBid,
  verifyAuctionStatus,
  testCheckout
};
