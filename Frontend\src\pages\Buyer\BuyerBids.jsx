import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { getUserBids, cancelBid } from "../../redux/slices/bidSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaGavel, FaEye, FaSync, FaTimes, FaCreditCard, FaClock, FaCheck } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerBids.css";

const BuyerBids = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userBids, isLoading, isError, error } = useSelector((state) => state.bid);

  const [selectedBid, setSelectedBid] = useState(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  // Fetch bids on component mount
  useEffect(() => {
    dispatch(getUserBids());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(getUserBids());
  };

  const handleCancelBid = async (bid) => {
    setIsCancelling(true);
    try {
      const response = await dispatch(cancelBid(bid._id)).unwrap();

      // Show appropriate message based on whether a previous bid was reactivated
      if (response.data?.reactivatedBid) {
        toast.success(`Bid cancelled and your previous $${response.data.reactivatedBid.amount} bid is now active`);
      } else {
        toast.success("Bid cancelled successfully");
      }

      setShowCancelModal(false);
      setSelectedBid(null);

      // Refresh bids
      dispatch(getUserBids());
    } catch (error) {
      toast.error(error.message || "Failed to cancel bid");
    } finally {
      setIsCancelling(false);
    }
  };

  const handlePayNow = (bid) => {
    // Check if bid has an order and redirect to the existing checkout page
    if (bid.order && bid.order._id) {
      navigate(`/checkout/${bid.order._id}`);
    } else {
      // Fallback to the old checkout page if no order exists
      navigate('/buyer/checkout', {
        state: {
          type: 'bid',
          bidId: bid._id,
          amount: bid.amount,
          content: bid.content
        }
      });
    }
  };

  const openCancelModal = (bid) => {
    setSelectedBid(bid);
    setShowCancelModal(true);
  };

  const closeCancelModal = () => {
    setShowCancelModal(false);
    setSelectedBid(null);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Active":
        return "status-active";
      case "Won":
        return "status-won";
      case "Lost":
        return "status-lost";
      case "Cancelled":
        return "status-cancelled";
      default:
        return "status-default";
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isAuctionActive = (content) => {
    if (!content?.auctionDetails) return false;

    const now = new Date();
    const startDate = content.auctionDetails.auctionStartDate ? new Date(content.auctionDetails.auctionStartDate) : null;
    const endDate = content.auctionDetails.auctionEndDate ? new Date(content.auctionDetails.auctionEndDate) : null;

    return (!startDate || now >= startDate) && (!endDate || now <= endDate);
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "bidId",
      label: "Bid Id",
      className: "bid-id",
    },
    {
      key: "content",
      label: "Videos/Documents",
      className: "video",
      render: (bid) => (
        <div className="content-item">
          <div className="content-image">
            <img
              src={bid.content?.thumbnailUrl || "https://via.placeholder.com/60x40"}
              alt={bid.content?.title || "Content"}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{bid.content?.title || "Unknown Content"}</div>
            <div className="content-coach">By {bid.content?.seller?.firstName || "Unknown"}</div>
          </div>
        </div>
      )
    },
    {
      key: "date",
      label: "Date",
      className: "date",
      render: (bid) => formatDate(bid.createdAt)
    },
    {
      key: "bidAmount",
      label: "Bid Amount",
      className: "bid-amount",
      render: (bid) => `$${bid.amount?.toFixed(2) || '0.00'}`
    },
    {
      key: "status",
      label: "Status",
      className: "status",
      render: (bid) => (
        <span className={`status-badge ${getStatusBadgeClass(bid.status)}`}>
          {bid.status}
        </span>
      )
    },
    {
      key: "auctionStatus",
      label: "Auction",
      className: "auction-status",
      render: (bid) => (
        <span className={`auction-indicator ${isAuctionActive(bid.content) ? 'active' : 'ended'}`}>
          <FaClock /> {isAuctionActive(bid.content) ? 'Active' : 'Ended'}
        </span>
      )
    },
    {
      key: "action",
      label: "Action",
      className: "action",
      render: (bid) => (
        <div className="action-buttons">
          {bid.status === "Won" ? (
            // Check if payment is completed
            bid.order && bid.order.paymentStatus === "Completed" ? (
              <button
                className="btn-paid"
                disabled
                style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  cursor: 'not-allowed',
                  opacity: 0.8
                }}
              >
                <FaCheck /> Already Paid
              </button>
            ) : (
              <button
                className="btn-pay"
                onClick={() => handlePayNow(bid)}
              >
                <FaCreditCard /> Pay Now
              </button>
            )
          ) : bid.status === "Active" ? (
            <button
              className="btn-cancel"
              onClick={() => openCancelModal(bid)}
            >
              <FaTimes /> Cancel
            </button>
          ) : (
            <button
              className="btn-view"
              onClick={() => navigate(`/buyer/details/${bid.content?._id}`)}
            >
              <FaEye /> View
            </button>
          )}
        </div>
      )
    },
  ];

  const formatData = (bids) => {
    return bids.map((bid, index) => ({
      ...bid,
      no: index + 1,
      bidId: `#${bid._id?.slice(-6) || 'N/A'}`,
    }));
  };

  return (
    <div className="BuyerBids">
      <SectionWrapper
        icon={<FaGavel className="BuyerSidebar__icon" />}
        title="My Bids"
        action={
          <button
            className="retry-btn"
            onClick={handleRetry}
            title="Refresh bids"
          >
            <FaSync />
          </button>
        }
      >
        {isError ? (
          <ErrorDisplay
            error={error}
            onRetry={handleRetry}
            title="Failed to load bids"
          />
        ) : isLoading ? (
          <div className="loading-container">
            <TableRowSkeleton columns={8} />
            <TableRowSkeleton columns={8} />
            <TableRowSkeleton columns={8} />
          </div>
        ) : userBids.length > 0 ? (
          <Table
            columns={columns}
            data={formatData(userBids)}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 1fr 1.5fr"
            className="BuyerBids__table"
            emptyMessage="You have no bids yet."
          />
        ) : (
          <div className="BuyerBids__empty">
            <FaGavel className="empty-icon" />
            <h3>No bids yet</h3>
            <p>You haven't placed any bids on auction content yet. Browse available auctions to start bidding.</p>
          </div>
        )}
      </SectionWrapper>

      {/* Cancel Bid Modal */}
      {showCancelModal && selectedBid && (
        <div className="cancel-modal-overlay">
          <div className="cancel-modal">
            <div className="modal-header">
              <h3>Cancel Bid</h3>
              <button
                className="close-btn"
                onClick={closeCancelModal}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="bid-details">
                <h4>{selectedBid.content?.title}</h4>
                <p><strong>Your Bid:</strong> ${selectedBid.amount?.toFixed(2)}</p>
                <p><strong>Bid Date:</strong> {formatDate(selectedBid.createdAt)}</p>
                <p><strong>Status:</strong> {selectedBid.status}</p>
              </div>

              <div className="warning-message">
                <p>Are you sure you want to cancel this bid? This action cannot be undone.</p>
              </div>

              <div className="modal-actions">
                <button
                  className="btn-secondary"
                  onClick={closeCancelModal}
                  disabled={isCancelling}
                >
                  Keep Bid
                </button>
                <button
                  className="btn-danger"
                  onClick={() => handleCancelBid(selectedBid)}
                  disabled={isCancelling}
                >
                  <FaTimes /> {isCancelling ? "Cancelling..." : "Cancel Bid"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BuyerBids;
