/**
 * Email templates for various notifications
 */

/**
 * Generate auction bid acceptance email template
 * @param {Object} data - Email data
 * @param {Object} data.bidder - Bidder information
 * @param {Object} data.content - Content information
 * @param {Object} data.bid - Bid information
 * @param {string} data.checkoutUrl - Checkout URL
 * @returns {Object} Email template with subject, text, and html
 */
const generateBidAcceptanceEmail = (data) => {
  const { bidder, content, bid, checkoutUrl } = data;
  
  const subject = `🎉 Congratulations! Your bid has been accepted - ${content.title}`;
  
  const textMessage = `
Congratulations ${bidder.firstName}!

Your bid of $${bid.amount.toFixed(2)} for "${content.title}" has been accepted by the seller.

Content Details:
- Title: ${content.title}
- Sport: ${content.sport}
- Content Type: ${content.contentType}
- Seller: ${content.seller.firstName} ${content.seller.lastName}

Your winning bid: $${bid.amount.toFixed(2)}

To complete your purchase and access the content, please click the link below:
${checkoutUrl}

This link will take you to our secure checkout page where you can complete your payment.

Important Notes:
- You have 24 hours to complete the payment
- After successful payment, you'll have immediate access to download the content
- If you have any questions, please contact our support team

Thank you for using XO Sports Hub!

Best regards,
The XO Sports Hub Team
  `;

  const htmlMessage = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
      <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2c5aa0; margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
          <p style="color: #666; font-size: 18px; margin: 10px 0 0 0;">Your bid has been accepted</p>
        </div>

        <!-- Greeting -->
        <div style="margin-bottom: 25px;">
          <p style="font-size: 16px; color: #333; margin: 0;">Dear ${bidder.firstName},</p>
        </div>

        <!-- Main Message -->
        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #2c5aa0;">
          <p style="font-size: 16px; color: #333; margin: 0 0 10px 0;">
            Great news! Your bid of <strong style="color: #2c5aa0;">$${bid.amount.toFixed(2)}</strong> for 
            "<strong>${content.title}</strong>" has been accepted by the seller.
          </p>
        </div>

        <!-- Content Details -->
        <div style="margin-bottom: 25px;">
          <h3 style="color: #2c5aa0; margin: 0 0 15px 0; font-size: 18px;">Content Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold; width: 30%;">Title:</td>
              <td style="padding: 8px 0; color: #333;">${content.title}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Sport:</td>
              <td style="padding: 8px 0; color: #333;">${content.sport}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Content Type:</td>
              <td style="padding: 8px 0; color: #333;">${content.contentType}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Seller:</td>
              <td style="padding: 8px 0; color: #333;">${content.seller.firstName} ${content.seller.lastName}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #666; font-weight: bold;">Your Winning Bid:</td>
              <td style="padding: 8px 0; color: #2c5aa0; font-weight: bold; font-size: 18px;">$${bid.amount.toFixed(2)}</td>
            </tr>
          </table>
        </div>

        <!-- CTA Button -->
        <div style="text-align: center; margin: 30px 0;">
          <a href="${checkoutUrl}" 
             style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; font-size: 16px; display: inline-block;">
            Complete Your Purchase
          </a>
        </div>

        <!-- Important Notes -->
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 25px;">
          <h4 style="color: #856404; margin: 0 0 10px 0; font-size: 16px;">Important Notes:</h4>
          <ul style="color: #856404; margin: 0; padding-left: 20px;">
            <li>You have 24 hours to complete the payment</li>
            <li>After successful payment, you'll have immediate access to download the content</li>
            <li>If you have any questions, please contact our support team</li>
          </ul>
        </div>

        <!-- Footer -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 14px; margin: 0;">
            Thank you for using XO Sports Hub!<br>
            <strong>The XO Sports Hub Team</strong>
          </p>
        </div>
      </div>
    </div>
  `;

  return {
    subject,
    message: textMessage,
    html: htmlMessage
  };
};

module.exports = {
  generateBidAcceptanceEmail
};
