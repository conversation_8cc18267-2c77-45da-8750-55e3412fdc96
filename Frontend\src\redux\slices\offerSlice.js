import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import offerService from "../../services/offerService";
import { formatError } from "../../utils/errorHandler";

// Initial state
const initialState = {
  offers: [],
  buyerOffers: [],
  sellerOffers: [],
  contentOffers: [],
  singleOffer: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
};

// Create offer
export const createOffer = createAsyncThunk(
  "offer/createOffer",
  async (offerData, thunkAPI) => {
    try {
      return await offerService.createOffer(offerData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Get buyer offers
export const getBuyerOffers = createAsyncThunk(
  "offer/getBuyerOffers",
  async (_, thunkAPI) => {
    try {
      return await offerService.getBuyerOffers();
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Get seller offers
export const getSellerOffers = createAsyncThunk(
  "offer/getSellerOffers",
  async (_, thunkAPI) => {
    try {
      return await offerService.getSellerOffers();
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Update offer status
export const updateOfferStatus = createAsyncThunk(
  "offer/updateOfferStatus",
  async ({ offerId, status, sellerResponse }, thunkAPI) => {
    try {
      return await offerService.updateOfferStatus(
        offerId,
        status,
        sellerResponse
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Cancel offer
export const cancelOffer = createAsyncThunk(
  "offer/cancelOffer",
  async (offerId, thunkAPI) => {
    try {
      return await offerService.cancelOffer(offerId);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Get content offers
export const getContentOffers = createAsyncThunk(
  "offer/getContentOffers",
  async (contentId, thunkAPI) => {
    try {
      return await offerService.getContentOffers(contentId);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Get single offer
export const getOffer = createAsyncThunk(
  "offer/getOffer",
  async (offerId, thunkAPI) => {
    try {
      return await offerService.getOffer(offerId);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

const offerSlice = createSlice({
  name: "offer",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },
    clearOffers: (state) => {
      state.offers = [];
      state.buyerOffers = [];
      state.sellerOffers = [];
      state.contentOffers = [];
      state.singleOffer = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create offer
      .addCase(createOffer.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(createOffer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.buyerOffers.unshift(action.payload.data);
      })
      .addCase(createOffer.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Get buyer offers
      .addCase(getBuyerOffers.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getBuyerOffers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.buyerOffers = action.payload.data;
      })
      .addCase(getBuyerOffers.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Get seller offers
      .addCase(getSellerOffers.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getSellerOffers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.sellerOffers = action.payload.data;
      })
      .addCase(getSellerOffers.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Get content offers
      .addCase(getContentOffers.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getContentOffers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.contentOffers = action.payload.data;
      })
      .addCase(getContentOffers.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Update offer status
      .addCase(updateOfferStatus.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(updateOfferStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        const updatedOffer = action.payload.data;

        // Update in seller offers
        const sellerIndex = state.sellerOffers.findIndex(
          (offer) => offer._id === updatedOffer._id
        );
        if (sellerIndex !== -1) {
          state.sellerOffers[sellerIndex] = updatedOffer;
        }

        // Update in content offers
        const contentIndex = state.contentOffers.findIndex(
          (offer) => offer._id === updatedOffer._id
        );
        if (contentIndex !== -1) {
          state.contentOffers[contentIndex] = updatedOffer;
        }
      })
      .addCase(updateOfferStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Cancel offer
      .addCase(cancelOffer.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(cancelOffer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        const cancelledOffer = action.payload.data;

        // Update in buyer offers
        const buyerIndex = state.buyerOffers.findIndex(
          (offer) => offer._id === cancelledOffer._id
        );
        if (buyerIndex !== -1) {
          state.buyerOffers[buyerIndex] = cancelledOffer;
        }
      })
      .addCase(cancelOffer.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      // Get single offer
      .addCase(getOffer.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getOffer.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.singleOffer = action.payload.data;
      })
      .addCase(getOffer.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

export const { reset, clearOffers } = offerSlice.actions;
export default offerSlice.reducer;
